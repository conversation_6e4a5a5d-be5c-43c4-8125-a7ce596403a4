package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 全息图管理器 - 管理搜刮箱上方的倒计时全息图
 */
public class HologramManager {

    private final ScavengePlugin plugin;
    private final Map<Location, ArmorStand> holograms;
    private final Map<Location, BukkitTask> updateTasks;

    public HologramManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.holograms = new HashMap<>();
        this.updateTasks = new HashMap<>();
    }

    /**
     * 为搜刮箱创建倒计时全息图
     */
    public void createCountdownHologram(Location chestLocation, long resetTimeMillis) {
        if (!plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            return;
        }

        // 移除已存在的全息图
        removeHologram(chestLocation);

        // 计算全息图位置
        double heightOffset = plugin.getConfig().getDouble("scavenge-chest.hologram.height-offset", 1.5);
        Location hologramLocation = chestLocation.clone().add(0.5, heightOffset, 0.5);

        // 创建盔甲架作为全息图
        ArmorStand hologram = (ArmorStand) chestLocation.getWorld().spawnEntity(hologramLocation,
                EntityType.ARMOR_STAND);
        hologram.setVisible(false);
        hologram.setGravity(false);
        hologram.setCanPickupItems(false);
        hologram.setCustomNameVisible(true);
        hologram.setMarker(true); // 防止碰撞
        hologram.setSmall(true);

        // 存储全息图
        holograms.put(chestLocation, hologram);

        // 开始倒计时更新任务
        startCountdownTask(chestLocation, resetTimeMillis);
    }

    /**
     * 为搜刮箱创建进度全息图
     */
    public void createProgressHologram(Location chestLocation, Player player) {
        if (!plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            return;
        }

        ScavengeChest chest = plugin.getScavengeChestManager().getChest(chestLocation);
        if (chest == null) {
            return;
        }

        // 移除已存在的全息图
        removeHologram(chestLocation);

        // 计算全息图位置
        double heightOffset = plugin.getConfig().getDouble("scavenge-chest.hologram.height-offset", 1.5);
        Location hologramLocation = chestLocation.clone().add(0.5, heightOffset, 0.5);

        // 创建盔甲架作为全息图
        ArmorStand hologram = (ArmorStand) chestLocation.getWorld().spawnEntity(hologramLocation,
                EntityType.ARMOR_STAND);
        hologram.setVisible(false);
        hologram.setGravity(false);
        hologram.setCanPickupItems(false);
        hologram.setCustomNameVisible(true);
        hologram.setMarker(true); // 防止碰撞
        hologram.setSmall(true);

        // 存储全息图
        holograms.put(chestLocation, hologram);

        // 开始进度更新任务
        startProgressTask(chestLocation, player);
    }

    /**
     * 开始倒计时更新任务
     */
    private void startCountdownTask(Location chestLocation, long resetTimeMillis) {
        int updateInterval = plugin.getConfig().getInt("scavenge-chest.hologram.update-interval", 1);
        String textFormat = plugin.getConfig().getString("scavenge-chest.hologram.text-format", "&c&l重置倒计时: &f{time}");

        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                ArmorStand hologram = holograms.get(chestLocation);
                if (hologram == null || hologram.isDead()) {
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    return;
                }

                long currentTime = System.currentTimeMillis();
                long remainingTime = resetTimeMillis - currentTime;

                if (remainingTime <= 0) {
                    // 倒计时结束，移除全息图
                    removeHologram(chestLocation);
                    this.cancel();
                    return;
                }

                // 格式化剩余时间
                String timeString = formatTime(remainingTime);
                String displayText = textFormat.replace("{time}", timeString).replace("&", "§");

                hologram.setCustomName(displayText);
            }
        }.runTaskTimer(plugin, 0L, updateInterval * 20L); // 转换为tick

        updateTasks.put(chestLocation, task);
    }

    /**
     * 开始进度更新任务
     */
    private void startProgressTask(Location chestLocation, Player player) {
        int updateInterval = plugin.getConfig().getInt("scavenge-chest.hologram.update-interval", 1);

        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                ArmorStand hologram = holograms.get(chestLocation);
                if (hologram == null || hologram.isDead()) {
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    return;
                }

                ScavengeChest chest = plugin.getScavengeChestManager().getChest(chestLocation);
                if (chest == null) {
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    return;
                }

                // 根据玩家权限获取总物品数量
                int totalItems = getTotalItemsForPlayer(player);
                int completedItems = chest.getCompletedSlots().size();
                int claimedItems = chest.getClaimedSlots().size();

                // 检查是否应该显示重置倒计时（完成或超额完成）
                if (chest.shouldShowResetCountdown()) {
                    // 显示倒计时
                    long resetTime = chest.getLastResetTime() +
                            (plugin.getConfig().getInt("scavenge-chest.reset-time", 300) * 1000L);

                    // 切换到倒计时模式
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    createCountdownHologram(chestLocation, resetTime);
                    return;
                }

                // 显示进度信息
                String displayText;
                if (chest.isCompleted()) {
                    // 所有物品已探索完成，显示领取进度
                    displayText = String.format("&a&l已完成 &f%d/%d &7(已领取: %d)",
                            completedItems, totalItems, claimedItems);
                } else {
                    // 还在探索中，显示探索进度
                    displayText = String.format("&e&l探索中 &f%d/%d &7(已领取: %d)",
                            completedItems, totalItems, claimedItems);
                }

                hologram.setCustomName(displayText.replace("&", "§"));
            }
        }.runTaskTimer(plugin, 0L, updateInterval * 20L); // 转换为tick

        updateTasks.put(chestLocation, task);
    }

    /**
     * 根据玩家权限获取总物品数量
     */
    private int getTotalItemsForPlayer(Player player) {
        if (player == null) {
            return plugin.getConfig().getInt("gui.random-items", 5);
        }

        // 检查权限配置，从高级权限到低级权限
        String[] permissions = {
                "scavenge.admin",
                "scavenge.mvp",
                "scavenge.svip",
                "scavenge.vip"
        };

        for (String permission : permissions) {
            if (player.hasPermission(permission)) {
                String path = "gui.permission-items." + permission + ".random-items";
                int items = plugin.getConfig().getInt(path, -1);
                if (items > 0) {
                    return items;
                }
            }
        }

        // 如果没有特殊权限，使用默认数量
        return plugin.getConfig().getInt("gui.random-items", 5);
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(long milliseconds) {
        long totalSeconds = milliseconds / 1000;

        long hours = TimeUnit.SECONDS.toHours(totalSeconds);
        long minutes = TimeUnit.SECONDS.toMinutes(totalSeconds) % 60;
        long seconds = totalSeconds % 60;

        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 移除指定位置的全息图
     */
    public void removeHologram(Location chestLocation) {
        // 停止更新任务
        BukkitTask task = updateTasks.remove(chestLocation);
        if (task != null) {
            task.cancel();
        }

        // 移除全息图实体
        ArmorStand hologram = holograms.remove(chestLocation);
        if (hologram != null && !hologram.isDead()) {
            hologram.remove();
        }
    }

    /**
     * 移除所有全息图
     */
    public void removeAllHolograms() {
        // 停止所有更新任务
        for (BukkitTask task : updateTasks.values()) {
            if (task != null) {
                task.cancel();
            }
        }
        updateTasks.clear();

        // 移除所有全息图实体
        for (ArmorStand hologram : holograms.values()) {
            if (hologram != null && !hologram.isDead()) {
                hologram.remove();
            }
        }
        holograms.clear();
    }

    /**
     * 检查指定位置是否有全息图
     */
    public boolean hasHologram(Location chestLocation) {
        return holograms.containsKey(chestLocation);
    }

    /**
     * 获取指定位置的全息图
     */
    public ArmorStand getHologram(Location chestLocation) {
        return holograms.get(chestLocation);
    }

    /**
     * 更新全息图的可见性（基于玩家距离）
     */
    public void updateHologramVisibility() {
        if (!plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            return;
        }

        double viewDistance = plugin.getConfig().getDouble("scavenge-chest.hologram.view-distance", 16.0);

        for (Map.Entry<Location, ArmorStand> entry : holograms.entrySet()) {
            Location chestLocation = entry.getKey();
            ArmorStand hologram = entry.getValue();

            if (hologram == null || hologram.isDead()) {
                continue;
            }

            // 检查附近是否有玩家
            boolean hasNearbyPlayer = false;
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.getWorld().equals(chestLocation.getWorld()) &&
                        player.getLocation().distance(chestLocation) <= viewDistance) {
                    hasNearbyPlayer = true;
                    break;
                }
            }

            // 根据是否有附近玩家来设置可见性
            hologram.setCustomNameVisible(hasNearbyPlayer);
        }
    }
}

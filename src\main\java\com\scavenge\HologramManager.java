package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 全息图管理器 - 管理搜刮箱上方的倒计时全息图
 */
public class HologramManager {

    private final ScavengePlugin plugin;
    private final Map<Location, ArmorStand> holograms;
    private final Map<Location, BukkitTask> updateTasks;

    public HologramManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.holograms = new HashMap<>();
        this.updateTasks = new HashMap<>();
    }

    /**
     * 为搜刮箱创建倒计时全息图
     */
    public void createCountdownHologram(Location chestLocation, long resetTimeMillis) {
        if (!plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            return;
        }

        // 移除已存在的全息图
        removeHologram(chestLocation);

        // 计算全息图位置
        double heightOffset = plugin.getConfig().getDouble("scavenge-chest.hologram.height-offset", 1.5);
        Location hologramLocation = chestLocation.clone().add(0.5, heightOffset, 0.5);

        // 创建盔甲架作为全息图
        ArmorStand hologram = (ArmorStand) chestLocation.getWorld().spawnEntity(hologramLocation,
                EntityType.ARMOR_STAND);
        hologram.setVisible(false);
        hologram.setGravity(false);
        hologram.setCanPickupItems(false);
        hologram.setCustomNameVisible(true);
        hologram.setMarker(true); // 防止碰撞
        hologram.setSmall(true);

        // 存储全息图
        holograms.put(chestLocation, hologram);

        // 开始倒计时更新任务
        startCountdownTask(chestLocation, resetTimeMillis);
    }

    /**
     * 为搜刮箱创建进度全息图
     */
    public void createProgressHologram(Location chestLocation, Player player) {
        if (!plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            return;
        }

        ScavengeChest chest = plugin.getScavengeChestManager().getChest(chestLocation);
        if (chest == null) {
            return;
        }

        // 移除已存在的全息图
        removeHologram(chestLocation);

        // 计算全息图位置
        double heightOffset = plugin.getConfig().getDouble("scavenge-chest.hologram.height-offset", 1.5);
        Location hologramLocation = chestLocation.clone().add(0.5, heightOffset, 0.5);

        // 创建盔甲架作为全息图
        ArmorStand hologram = (ArmorStand) chestLocation.getWorld().spawnEntity(hologramLocation,
                EntityType.ARMOR_STAND);
        hologram.setVisible(false);
        hologram.setGravity(false);
        hologram.setCanPickupItems(false);
        hologram.setCustomNameVisible(true);
        hologram.setMarker(true); // 防止碰撞
        hologram.setSmall(true);

        // 存储全息图
        holograms.put(chestLocation, hologram);

        // 开始进度更新任务
        startProgressTask(chestLocation, player);
    }

    /**
     * 开始倒计时更新任务
     */
    private void startCountdownTask(Location chestLocation, long resetTimeMillis) {
        int updateInterval = plugin.getConfig().getInt("scavenge-chest.hologram.update-interval", 1);
        String textFormat = plugin.getConfig().getString("scavenge-chest.hologram.text-format", "&c&l重置倒计时: &f{time}");

        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                ArmorStand hologram = holograms.get(chestLocation);
                if (hologram == null || hologram.isDead()) {
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    return;
                }

                long currentTime = System.currentTimeMillis();
                long remainingTime = resetTimeMillis - currentTime;

                if (remainingTime <= 0) {
                    // 倒计时结束，执行重置
                    ScavengeChest chest = plugin.getScavengeChestManager().getChest(chestLocation);
                    if (chest != null) {
                        // 强制重置搜刮箱（包括关闭GUI）
                        chest.reset();
                        plugin.getLogger().info("搜刮箱自动重置: " + chestLocation.toString());
                    }

                    // 移除全息图和任务
                    removeHologram(chestLocation);
                    this.cancel();
                    return;
                }

                // 格式化剩余时间
                String timeString = formatTime(remainingTime);
                String displayText = textFormat.replace("{time}", timeString).replace("&", "§");

                hologram.setCustomName(displayText);
            }
        }.runTaskTimer(plugin, 0L, updateInterval * 20L); // 转换为tick

        updateTasks.put(chestLocation, task);
    }

    /**
     * 开始进度更新任务
     */
    private void startProgressTask(Location chestLocation, Player player) {
        int updateInterval = plugin.getConfig().getInt("scavenge-chest.hologram.update-interval", 1);

        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                ArmorStand hologram = holograms.get(chestLocation);
                if (hologram == null || hologram.isDead()) {
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    return;
                }

                ScavengeChest chest = plugin.getScavengeChestManager().getChest(chestLocation);
                if (chest == null) {
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    return;
                }

                // 检测当前开箱玩家的权限，获取对应的物品数量
                Player currentPlayer = getCurrentChestPlayer(chest);
                int totalItems = getTotalItemsForPlayer(currentPlayer);

                int completedItems = chest.getCompletedSlots().size();
                int claimedItems = chest.getClaimedSlots().size();
                int searchingItems = chest.getSearchingSlots().size();

                // 计算已探索的物品总数（已完成 + 已领取 + 搜索中）
                int exploredItems = completedItems + claimedItems + searchingItems;

                plugin.getLogger().info("=== 全息图权限检测 ===");
                plugin.getLogger().info("当前开箱玩家: " + (currentPlayer != null ? currentPlayer.getName() : "无玩家"));
                plugin.getLogger().info("玩家权限物品数量: " + totalItems);
                plugin.getLogger().info("已探索物品: " + exploredItems);
                plugin.getLogger().info("已领取物品: " + claimedItems);

                // 检查是否超额完成（已探索数量超过总物品限制）
                if (exploredItems > totalItems) {
                    // 超额完成，强制设置超额完成状态和重置时间
                    if (!chest.isOverCompleted()) {
                        chest.setOverCompleted(true);
                        if (chest.getLastResetTime() == 0) {
                            chest.setLastResetTime(System.currentTimeMillis());
                        }
                        plugin.getLogger().info("全息图检测到超额完成: " + exploredItems + "/" + totalItems);
                    }
                }

                // 检查是否根据当前权限已经完成搜刮
                boolean isCompletedForCurrentPermission = (exploredItems >= totalItems);

                // 如果根据当前权限已完成但还没设置重置时间，设置重置时间
                if (isCompletedForCurrentPermission && chest.getLastResetTime() == 0) {
                    chest.setLastResetTime(System.currentTimeMillis());
                    plugin.getLogger().info("全息图检测到权限完成，设置重置时间: " + totalItems + " 物品已探索完成");
                }

                // 检查是否应该显示重置倒计时
                if (chest.shouldShowResetCountdown()
                        || (isCompletedForCurrentPermission && chest.getLastResetTime() > 0)) {
                    // 显示倒计时
                    long resetTime = chest.getLastResetTime() +
                            (plugin.getConfig().getInt("scavenge-chest.reset-time", 300) * 1000L);

                    // 切换到倒计时模式
                    this.cancel();
                    updateTasks.remove(chestLocation);
                    createCountdownHologram(chestLocation, resetTime);
                    return;
                }

                // 显示进度信息
                String displayText;
                if (isCompletedForCurrentPermission) {
                    // 根据当前权限已完成，显示完成状态
                    displayText = String.format("&a&l已完成 &f%d/%d &7(已领取: %d)",
                            exploredItems, totalItems, claimedItems);
                } else {
                    // 还在探索中，显示探索进度
                    displayText = String.format("&e&l探索中 &f%d/%d &7(已领取: %d)",
                            exploredItems, totalItems, claimedItems);
                }

                hologram.setCustomName(displayText.replace("&", "§"));
            }
        }.runTaskTimer(plugin, 0L, updateInterval * 20L); // 转换为tick

        updateTasks.put(chestLocation, task);
    }

    /**
     * 获取当前开箱的玩家
     */
    private Player getCurrentChestPlayer(ScavengeChest chest) {
        // 如果搜刮箱有活跃玩家，返回活跃玩家
        if (chest.getActivePlayer() != null) {
            Player activePlayer = Bukkit.getPlayer(chest.getActivePlayer());
            if (activePlayer != null && activePlayer.isOnline()) {
                return activePlayer;
            }
        }

        // 如果没有活跃玩家，检查附近是否有玩家正在查看搜刮箱
        Location chestLocation = chest.getLocation();
        double viewDistance = plugin.getConfig().getDouble("scavenge-chest.hologram.view-distance", 16.0);

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.getWorld().equals(chestLocation.getWorld()) &&
                    player.getLocation().distance(chestLocation) <= viewDistance) {

                // 检查玩家是否正在查看搜刮箱GUI
                if (player.getOpenInventory() != null &&
                        player.getOpenInventory().getTitle().contains("搜刮箱")) {
                    return player;
                }
            }
        }

        // 如果都没有，返回附近第一个玩家作为参考
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.getWorld().equals(chestLocation.getWorld()) &&
                    player.getLocation().distance(chestLocation) <= viewDistance) {
                return player;
            }
        }

        return null;
    }

    /**
     * 获取玩家权限等级（数字越大权限越高）
     */
    private int getPlayerPermissionLevel(Player player) {
        if (player.hasPermission("scavenge.admin"))
            return 4;
        if (player.hasPermission("scavenge.mvp"))
            return 3;
        if (player.hasPermission("scavenge.svip"))
            return 2;
        if (player.hasPermission("scavenge.vip"))
            return 1;
        return 0; // 默认权限
    }

    /**
     * 根据玩家权限获取总物品数量
     */
    private int getTotalItemsForPlayer(Player player) {
        if (player == null) {
            return plugin.getConfig().getInt("gui.random-items", 5);
        }

        // 检查权限配置，从高级权限到低级权限
        String[] permissions = {
                "scavenge.admin",
                "scavenge.mvp",
                "scavenge.svip",
                "scavenge.vip"
        };

        for (String permission : permissions) {
            if (player.hasPermission(permission)) {
                String path = "gui.permission-items." + permission + ".random-items";
                int items = plugin.getConfig().getInt(path, -1);
                if (items > 0) {
                    return items;
                }
            }
        }

        // 如果没有特殊权限，使用默认数量
        return plugin.getConfig().getInt("gui.random-items", 5);
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(long milliseconds) {
        long totalSeconds = milliseconds / 1000;

        long hours = TimeUnit.SECONDS.toHours(totalSeconds);
        long minutes = TimeUnit.SECONDS.toMinutes(totalSeconds) % 60;
        long seconds = totalSeconds % 60;

        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 移除指定位置的全息图
     */
    public void removeHologram(Location chestLocation) {
        // 停止更新任务
        BukkitTask task = updateTasks.remove(chestLocation);
        if (task != null) {
            task.cancel();
        }

        // 移除全息图实体
        ArmorStand hologram = holograms.remove(chestLocation);
        if (hologram != null && !hologram.isDead()) {
            hologram.remove();
        }
    }

    /**
     * 移除所有全息图
     */
    public void removeAllHolograms() {
        // 停止所有更新任务
        for (BukkitTask task : updateTasks.values()) {
            if (task != null) {
                task.cancel();
            }
        }
        updateTasks.clear();

        // 移除所有全息图实体
        for (ArmorStand hologram : holograms.values()) {
            if (hologram != null && !hologram.isDead()) {
                hologram.remove();
            }
        }
        holograms.clear();
    }

    /**
     * 检查指定位置是否有全息图
     */
    public boolean hasHologram(Location chestLocation) {
        return holograms.containsKey(chestLocation);
    }

    /**
     * 获取指定位置的全息图
     */
    public ArmorStand getHologram(Location chestLocation) {
        return holograms.get(chestLocation);
    }

    /**
     * 更新全息图的可见性（基于玩家距离）
     */
    public void updateHologramVisibility() {
        if (!plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            return;
        }

        double viewDistance = plugin.getConfig().getDouble("scavenge-chest.hologram.view-distance", 16.0);

        for (Map.Entry<Location, ArmorStand> entry : holograms.entrySet()) {
            Location chestLocation = entry.getKey();
            ArmorStand hologram = entry.getValue();

            if (hologram == null || hologram.isDead()) {
                continue;
            }

            // 检查附近是否有玩家
            boolean hasNearbyPlayer = false;
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.getWorld().equals(chestLocation.getWorld()) &&
                        player.getLocation().distance(chestLocation) <= viewDistance) {
                    hasNearbyPlayer = true;
                    break;
                }
            }

            // 根据是否有附近玩家来设置可见性
            hologram.setCustomNameVisible(hasNearbyPlayer);
        }
    }
}

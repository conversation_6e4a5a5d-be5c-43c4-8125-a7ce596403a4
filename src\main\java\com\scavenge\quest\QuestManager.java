package com.scavenge.quest;

import com.scavenge.ScavengePlugin;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务管理器
 */
public class QuestManager {

    private final ScavengePlugin plugin;
    private final Map<String, ScavengeQuest> quests;
    private final Map<UUID, Map<String, PlayerQuestProgress>> playerProgress;
    private File questDataFile;
    private FileConfiguration questDataConfig;
    private File questConfigFile;
    private FileConfiguration questConfig;

    public QuestManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.quests = new HashMap<>();
        this.playerProgress = new HashMap<>();

        setupQuestData();
        setupQuestConfig();
        loadQuests();
        loadPlayerProgress();
        startQuestUpdateTask();
    }

    /**
     * 设置任务数据文件
     */
    private void setupQuestData() {
        questDataFile = new File(plugin.getDataFolder(), "quest_data.yml");
        if (!questDataFile.exists()) {
            try {
                questDataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().warning("无法创建任务数据文件: " + e.getMessage());
            }
        }
        questDataConfig = YamlConfiguration.loadConfiguration(questDataFile);
    }

    /**
     * 设置任务配置文件
     */
    private void setupQuestConfig() {
        questConfigFile = new File(plugin.getDataFolder(), "quests.yml");
        if (!questConfigFile.exists()) {
            plugin.saveResource("quests.yml", false);
        }
        questConfig = YamlConfiguration.loadConfiguration(questConfigFile);
    }

    /**
     * 从配置文件加载任务
     */
    private void loadQuests() {
        ConfigurationSection questSection = questConfig.getConfigurationSection("quests");
        if (questSection == null) {
            plugin.getLogger().warning("任务配置文件中未找到任务配置，创建默认任务");
            createDefaultQuests();
            return;
        }

        for (String questId : questSection.getKeys(false)) {
            ConfigurationSection quest = questSection.getConfigurationSection(questId);
            if (quest != null) {
                try {
                    String name = quest.getString("name", "未命名任务");
                    String description = quest.getString("description", "无描述");
                    ScavengeQuest.QuestType type = ScavengeQuest.QuestType.valueOf(
                            quest.getString("type", "DAILY").toUpperCase());
                    ScavengeQuest.QuestGoal goal = ScavengeQuest.QuestGoal.valueOf(
                            quest.getString("goal", "SCAVENGE_COUNT").toUpperCase());
                    int targetAmount = quest.getInt("target-amount", 1);
                    List<String> rewards = quest.getStringList("rewards");
                    boolean console = quest.getBoolean("console", false);

                    // 检查是否是特定物品收集任务
                    String targetMaterial = quest.getString("target-material", null);
                    String targetDisplayName = quest.getString("target-display-name", null);

                    ScavengeQuest scavengeQuest;
                    if (targetMaterial != null && (goal == ScavengeQuest.QuestGoal.COLLECT_SPECIFIC ||
                            goal == ScavengeQuest.QuestGoal.COLLECT_COMMAND_ITEM)) {
                        scavengeQuest = new ScavengeQuest(questId, name, description,
                                type, goal, targetAmount, rewards, console, targetMaterial, targetDisplayName);
                    } else {
                        scavengeQuest = new ScavengeQuest(questId, name, description,
                                type, goal, targetAmount, rewards, console);
                    }

                    // 设置任务时间
                    long currentTime = System.currentTimeMillis();
                    scavengeQuest.setStartTime(currentTime);
                    switch (type) {
                        case DAILY:
                            scavengeQuest.setEndTime(currentTime + (24 * 60 * 60 * 1000L));
                            break;
                        case WEEKLY:
                            scavengeQuest.setEndTime(currentTime + (7 * 24 * 60 * 60 * 1000L));
                            break;
                        case SPECIAL:
                            scavengeQuest.setEndTime(currentTime + (30 * 24 * 60 * 60 * 1000L));
                            break;
                    }

                    quests.put(questId, scavengeQuest);

                    plugin.getLogger().info("加载任务: " + name);
                } catch (Exception e) {
                    plugin.getLogger().warning("加载任务 " + questId + " 时出错: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 创建默认任务
     */
    private void createDefaultQuests() {
        // 每日任务：搜刮5次
        ScavengeQuest dailyScavenge = new ScavengeQuest(
                "daily_scavenge_5",
                "每日搜刮者",
                "完成5次搜刮",
                ScavengeQuest.QuestType.DAILY,
                ScavengeQuest.QuestGoal.SCAVENGE_COUNT,
                5,
                Arrays.asList("give {player} diamond 3", "give {player} emerald 5"),
                true);

        // 每周任务：完成20个搜刮箱
        ScavengeQuest weeklyComplete = new ScavengeQuest(
                "weekly_complete_20",
                "每周探索者",
                "完成20个搜刮箱的搜刮",
                ScavengeQuest.QuestType.WEEKLY,
                ScavengeQuest.QuestGoal.COMPLETE_CHESTS,
                20,
                Arrays.asList("give {player} diamond_block 2", "give {player} experience_bottle 10"),
                true);

        quests.put(dailyScavenge.getId(), dailyScavenge);
        quests.put(weeklyComplete.getId(), weeklyComplete);
    }

    /**
     * 加载玩家进度
     */
    private void loadPlayerProgress() {
        ConfigurationSection progressSection = questDataConfig.getConfigurationSection("player_progress");
        if (progressSection == null)
            return;

        for (String playerIdStr : progressSection.getKeys(false)) {
            try {
                UUID playerId = UUID.fromString(playerIdStr);
                ConfigurationSection playerSection = progressSection.getConfigurationSection(playerIdStr);
                Map<String, PlayerQuestProgress> playerQuests = new HashMap<>();

                for (String questId : playerSection.getKeys(false)) {
                    ConfigurationSection questProgress = playerSection.getConfigurationSection(questId);
                    PlayerQuestProgress progress = new PlayerQuestProgress(playerId, questId);
                    progress.setProgress(questProgress.getInt("progress", 0));
                    progress.setCompleted(questProgress.getBoolean("completed", false));
                    progress.setClaimed(questProgress.getBoolean("claimed", false));
                    progress.setCompletedTime(questProgress.getLong("completed-time", 0));

                    playerQuests.put(questId, progress);
                }

                playerProgress.put(playerId, playerQuests);
            } catch (Exception e) {
                plugin.getLogger().warning("加载玩家任务进度时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 保存玩家进度
     */
    public void savePlayerProgress() {
        questDataConfig.set("player_progress", null); // 清空现有数据

        for (Map.Entry<UUID, Map<String, PlayerQuestProgress>> entry : playerProgress.entrySet()) {
            String playerIdStr = entry.getKey().toString();
            for (Map.Entry<String, PlayerQuestProgress> questEntry : entry.getValue().entrySet()) {
                String questId = questEntry.getKey();
                PlayerQuestProgress progress = questEntry.getValue();

                String path = "player_progress." + playerIdStr + "." + questId;
                questDataConfig.set(path + ".progress", progress.getCurrentProgress());
                questDataConfig.set(path + ".completed", progress.isCompleted());
                questDataConfig.set(path + ".claimed", progress.isClaimed());
                questDataConfig.set(path + ".completed-time", progress.getCompletedTime());
            }
        }

        try {
            questDataConfig.save(questDataFile);
        } catch (IOException e) {
            plugin.getLogger().warning("保存任务数据时出错: " + e.getMessage());
        }
    }

    /**
     * 启动任务更新任务
     */
    private void startQuestUpdateTask() {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            // 检查过期任务并重置
            checkAndResetExpiredQuests();
            // 定期保存数据
            savePlayerProgress();
        }, 20L * 60L, 20L * 60L); // 每分钟检查一次
    }

    /**
     * 检查并重置过期任务
     */
    private void checkAndResetExpiredQuests() {
        for (ScavengeQuest quest : quests.values()) {
            if (quest.isExpired()) {
                // 重置任务时间
                resetQuestTime(quest);
                // 清除所有玩家的进度
                clearQuestProgress(quest.getId());
            }
        }
    }

    /**
     * 重置任务时间
     */
    private void resetQuestTime(ScavengeQuest quest) {
        long currentTime = System.currentTimeMillis();
        quest.setStartTime(currentTime);

        switch (quest.getType()) {
            case DAILY:
                quest.setEndTime(currentTime + (24 * 60 * 60 * 1000L));
                break;
            case WEEKLY:
                quest.setEndTime(currentTime + (7 * 24 * 60 * 60 * 1000L));
                break;
            case SPECIAL:
                quest.setEndTime(currentTime + (30 * 24 * 60 * 60 * 1000L));
                break;
        }
    }

    /**
     * 清除任务进度
     */
    private void clearQuestProgress(String questId) {
        for (Map<String, PlayerQuestProgress> playerQuests : playerProgress.values()) {
            playerQuests.remove(questId);
        }
    }

    /**
     * 获取玩家任务进度
     */
    public PlayerQuestProgress getPlayerProgress(UUID playerId, String questId) {
        return playerProgress.computeIfAbsent(playerId, k -> new HashMap<>())
                .computeIfAbsent(questId, k -> new PlayerQuestProgress(playerId, questId));
    }

    /**
     * 更新玩家任务进度
     */
    public void updateQuestProgress(UUID playerId, ScavengeQuest.QuestGoal goalType, int amount) {
        plugin.getLogger().info("=== 任务进度更新开始 ===");
        plugin.getLogger().info("玩家: " + playerId);
        plugin.getLogger().info("目标类型: " + goalType);
        plugin.getLogger().info("数量: " + amount);
        plugin.getLogger().info("当前任务总数: " + quests.size());

        boolean foundMatchingQuest = false;
        for (ScavengeQuest quest : quests.values()) {
            plugin.getLogger().info("--- 检查任务: " + quest.getName() + " ---");
            plugin.getLogger().info("任务ID: " + quest.getId());
            plugin.getLogger().info("任务目标: " + quest.getGoal());
            plugin.getLogger().info("目标匹配: " + (quest.getGoal() == goalType));
            plugin.getLogger().info("任务活跃: " + quest.isActive());
            plugin.getLogger().info("开始时间: " + quest.getStartTime());
            plugin.getLogger().info("结束时间: " + quest.getEndTime());
            plugin.getLogger().info("当前时间: " + System.currentTimeMillis());
            plugin.getLogger().info("任务过期: " + quest.isExpired());
            plugin.getLogger().info("条件满足: " + (quest.getGoal() == goalType && quest.isActive() && !quest.isExpired()));

            if (quest.getGoal() == goalType && quest.isActive() && !quest.isExpired()) {
                foundMatchingQuest = true;
                PlayerQuestProgress progress = getPlayerProgress(playerId, quest.getId());
                plugin.getLogger().info("当前进度: " + progress.getCurrentProgress() + "/" + quest.getTargetAmount());
                plugin.getLogger().info("已完成: " + progress.isCompleted());

                if (!progress.isCompleted()) {
                    progress.addProgress(amount);
                    plugin.getLogger().info("更新后进度: " + progress.getCurrentProgress() + "/" + quest.getTargetAmount());

                    if (progress.checkCompletion(quest.getTargetAmount())) {
                        plugin.getLogger().info("任务完成!");
                        // 任务完成，通知玩家
                        Player player = Bukkit.getPlayer(playerId);
                        if (player != null) {
                            player.sendMessage(plugin.getMessage("quest-completed")
                                    .replace("{quest}", quest.getName()));
                        }
                    }
                } else {
                    plugin.getLogger().info("任务已完成，跳过更新");
                }
            }
        }

        if (!foundMatchingQuest) {
            plugin.getLogger().warning("=== 没有找到匹配的任务! ===");
            plugin.getLogger().warning("目标类型: " + goalType);
            plugin.getLogger().warning("可用任务:");
            for (ScavengeQuest quest : quests.values()) {
                plugin.getLogger().warning("- " + quest.getName() + " (目标: " + quest.getGoal() +
                        ", 活跃: " + quest.isActive() + ", 过期: " + quest.isExpired() + ")");
            }
        }

        plugin.getLogger().info("=== 任务进度更新结束 ===");

        // 立即保存数据
        savePlayerProgress();
    }

    /**
     * 更新特定物品收集任务进度
     */
    public void updateItemCollectionProgress(UUID playerId, String material, String displayName, int amount) {
        for (ScavengeQuest quest : quests.values()) {
            if ((quest.getGoal() == ScavengeQuest.QuestGoal.COLLECT_SPECIFIC ||
                    quest.getGoal() == ScavengeQuest.QuestGoal.COLLECT_COMMAND_ITEM) &&
                    quest.isActive() && !quest.isExpired()) {

                // 检查材质匹配
                if (quest.getTargetMaterial() != null && quest.getTargetMaterial().equalsIgnoreCase(material)) {
                    // 如果指定了显示名称，也要匹配显示名称
                    if (quest.getTargetDisplayName() == null ||
                            (displayName != null && displayName.contains(quest.getTargetDisplayName()))) {

                        PlayerQuestProgress progress = getPlayerProgress(playerId, quest.getId());
                        if (!progress.isCompleted()) {
                            progress.addProgress(amount);
                            if (progress.checkCompletion(quest.getTargetAmount())) {
                                // 任务完成，通知玩家
                                Player player = Bukkit.getPlayer(playerId);
                                if (player != null) {
                                    player.sendMessage(plugin.getMessage("quest-completed")
                                            .replace("{quest}", quest.getName()));
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 领取任务奖励
     */
    public boolean claimQuestReward(UUID playerId, String questId) {
        ScavengeQuest quest = quests.get(questId);
        if (quest == null) {
            plugin.getLogger().warning("任务不存在: " + questId);
            return false;
        }

        PlayerQuestProgress progress = getPlayerProgress(playerId, questId);

        // 检查任务是否完成（使用进度判断，而不是completed字段）
        boolean isCompleted = progress.getCurrentProgress() >= quest.getTargetAmount();
        if (!isCompleted) {
            plugin.getLogger().warning(
                    "任务未完成: " + questId + ", 进度: " + progress.getCurrentProgress() + "/" + quest.getTargetAmount());
            return false;
        }

        if (progress.isClaimed()) {
            plugin.getLogger().warning("任务奖励已领取: " + questId);
            return false;
        }

        Player player = Bukkit.getPlayer(playerId);
        if (player == null) {
            plugin.getLogger().warning("玩家不在线: " + playerId);
            return false;
        }

        plugin.getLogger().info("开始领取任务奖励: " + quest.getName() + " (玩家: " + player.getName() + ")");

        // 执行奖励命令
        for (String reward : quest.getRewards()) {
            String command = reward.replace("{player}", player.getName());
            plugin.getLogger().info("执行奖励命令: " + command);
            if (quest.isConsole()) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
            } else {
                Bukkit.dispatchCommand(player, command);
            }
        }

        // 标记为已完成和已领取
        progress.setCompleted(true, quest.getTargetAmount());
        progress.setClaimed(true);
        progress.setCompletedTime(System.currentTimeMillis());

        // 保存进度数据
        savePlayerProgress();

        player.sendMessage(plugin.getMessage("quest-reward-claimed")
                .replace("{quest}", quest.getName()));

        plugin.getLogger().info("任务奖励领取成功: " + quest.getName());
        return true;
    }

    /**
     * 获取玩家的活跃任务
     */
    public List<ScavengeQuest> getActiveQuests(UUID playerId) {
        return quests.values().stream()
                .filter(quest -> quest.isActive() && !quest.isExpired())
                .collect(Collectors.toList());
    }

    /**
     * 获取玩家已完成但未领取的任务
     */
    public List<ScavengeQuest> getCompletedQuests(UUID playerId) {
        return quests.values().stream()
                .filter(quest -> {
                    PlayerQuestProgress progress = getPlayerProgress(playerId, quest.getId());
                    return progress.isCompleted() && !progress.isClaimed();
                })
                .collect(Collectors.toList());
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        savePlayerProgress();
    }

    // Getters
    public Map<String, ScavengeQuest> getQuests() {
        return quests;
    }

    public Map<UUID, Map<String, PlayerQuestProgress>> getPlayerProgress() {
        return playerProgress;
    }
}

# 超额完成功能说明

## 🎯 **功能概述**

当高权限玩家降级到低权限时，如果已探索的物品数量超过了低权限的限制，系统会：

1. **标记为超额完成** - 设置 `isOverCompleted = true`
2. **显示重置倒计时全息图** - 立即显示重置时间
3. **阻止继续搜索** - 右键箱子显示提示并关闭GUI
4. **等待重置** - 只有重置后才能继续使用

## 🔧 **实现细节**

### **核心逻辑**

#### **1. 超额完成检测**
在 `ScavengeChest.adaptToPlayerPermission()` 方法中：

```java
// 检查是否已探索的物品数量超过了新权限限制
int exploredItems = searchingSlots.size() + completedSlots.size() + claimedSlots.size();
if (exploredItems >= newItemCount) {
    plugin.getLogger().info("已探索物品数量(" + exploredItems + ")超过新权限限制(" + newItemCount + ")，标记为超额完成");
    // 标记为超额完成状态
    this.isOverCompleted = true;
    // 设置完成时间用于重置倒计时
    if (this.lastResetTime == 0) {
        this.lastResetTime = System.currentTimeMillis();
    }
}
```

#### **2. GUI访问控制**
在 `ScavengeChestGUI.setupGUI()` 方法中：

```java
// 检查是否超额完成，如果是则显示重置倒计时并关闭GUI
if (scavengeChest.isOverCompleted()) {
    player.sendMessage(plugin.getMessage("chest-over-completed")
            .replace("{time}", String.valueOf(scavengeChest.getRemainingCooldownSeconds())));
    
    // 创建重置倒计时全息图
    createResetHologram();
    
    // 关闭GUI，不允许继续搜索
    player.closeInventory();
    return;
}
```

#### **3. 全息图显示**
在 `HologramManager.startProgressTask()` 方法中：

```java
// 检查是否应该显示重置倒计时（完成或超额完成）
if (chest.shouldShowResetCountdown()) {
    // 显示倒计时
    long resetTime = chest.getLastResetTime() +
            (plugin.getConfig().getInt("scavenge-chest.reset-time", 300) * 1000L);
    
    // 切换到倒计时模式
    this.cancel();
    updateTasks.remove(chestLocation);
    createCountdownHologram(chestLocation, resetTime);
    return;
}
```

## 🎮 **使用场景**

### **场景1：VIP降级到普通玩家**

#### **初始状态**
- **VIP玩家A**: 10个物品权限
- **搜刮进度**: 已搜索8个物品（6个已完成，2个已领取）
- **状态**: `已搜索:0, 已完成:6, 已领取:2, 未搜索:2`

#### **权限降级**
- **玩家A权限被移除**: 现在只有5个物品权限
- **智能适配检测**: 已探索物品(8个) >= 新权限限制(5个)
- **系统响应**: 标记为超额完成

#### **玩家体验**
1. **右键搜刮箱**: 显示提示消息并关闭GUI
2. **提示消息**: `"权限不足！你的权限已降级，此搜刮箱已超出你的权限范围。请等待 245秒 后重置。"`
3. **全息图**: 显示重置倒计时
4. **等待重置**: 只有等待重置时间结束才能重新使用

### **场景2：SVIP降级到VIP**

#### **初始状态**
- **SVIP玩家B**: 15个物品权限
- **搜刮进度**: 已搜索12个物品（10个已完成，2个已领取）
- **状态**: `已搜索:0, 已完成:10, 已领取:2, 未搜索:3`

#### **权限降级**
- **玩家B降级到VIP**: 现在有10个物品权限
- **智能适配检测**: 已探索物品(12个) >= 新权限限制(10个)
- **系统响应**: 标记为超额完成

#### **预期日志**
```
[INFO] === 智能权限适配 ===
[INFO] 玩家: PlayerB
[INFO] 新权限物品数量: 10
[INFO] 当前总槽位数: 15
[INFO] 需要移除物品数量: 5
[INFO] 移除未搜索物品槽位: 12
[INFO] 移除未搜索物品槽位: 13
[INFO] 移除未搜索物品槽位: 14
[INFO] 已探索物品数量(12)超过新权限限制(10)，标记为超额完成
[INFO] === 适配完成 ===
```

## 📊 **状态检查方法**

### **新增方法**

#### **1. `isOverCompleted()`**
```java
public boolean isOverCompleted() {
    return isOverCompleted;
}
```
检查是否超额完成状态。

#### **2. `shouldShowResetCountdown()`**
```java
public boolean shouldShowResetCountdown() {
    return (isCompleted() && isAllItemsClaimed()) || isOverCompleted();
}
```
检查是否应该显示重置倒计时（正常完成或超额完成）。

### **状态优先级**
1. **超额完成** > **正常完成** > **进行中**
2. **超额完成时**: 立即显示倒计时，阻止访问
3. **正常完成时**: 允许领取剩余奖励，全部领取后显示倒计时

## 🔄 **重置机制**

### **重置条件**
- 重置时间到达后，`isOverCompleted` 状态会被清除
- 重置后搜刮箱恢复正常状态
- 玩家可以根据当前权限重新使用搜刮箱

### **重置过程**
1. **时间检查**: `canReset()` 返回 `true`
2. **状态清除**: `isOverCompleted = false`
3. **重新初始化**: 根据玩家当前权限重新设置物品数量
4. **全息图更新**: 移除倒计时，显示新的进度

## 💬 **消息配置**

### **config.yml 新增消息**
```yaml
messages:
  chest-over-completed: "&c&l权限不足！&r&e你的权限已降级，此搜刮箱已超出你的权限范围。请等待 &c{time}秒 &e后重置。"
```

### **消息变量**
- `{time}`: 剩余重置时间（秒）

## 🎯 **全息图显示**

### **超额完成时的全息图**
```
§c§l重置倒计时: §f4分32秒
§7权限不足，等待重置
```

### **正常完成时的全息图**
```
§c§l重置倒计时: §f4分32秒
§7已完成: 5/5 物品
```

## 🧪 **测试场景**

### **测试1：权限降级检测**
1. **VIP玩家打开搜刮箱** → 显示10个物品
2. **搜索8个物品** → 6个已完成，2个已领取
3. **移除VIP权限** → 降级到普通玩家(5个物品)
4. **再次右键搜刮箱** → 显示超额完成提示，关闭GUI
5. **检查全息图** → 显示重置倒计时

### **测试2：重置后恢复**
1. **等待重置时间结束** → 5分钟后
2. **右键搜刮箱** → 正常打开，显示5个新物品
3. **检查全息图** → 显示正常进度

### **测试3：多次权限变化**
1. **普通玩家** → 5个物品，搜索3个
2. **升级到VIP** → 适配到10个物品，继续搜索
3. **降级到普通** → 已搜索7个 > 5个限制，标记超额完成
4. **验证状态** → 阻止访问，显示倒计时

## 🚀 **优势特性**

### **1. 公平性保证**
- 防止权限降级后继续享受高权限福利
- 确保每个权限等级的物品数量限制得到执行
- 维护服务器经济平衡

### **2. 用户体验**
- 清晰的提示消息告知玩家状态
- 倒计时显示让玩家知道何时可以重新使用
- 不会丢失已获得的奖励

### **3. 系统完整性**
- 与现有的智能权限适配系统无缝集成
- 保持数据一致性和状态同步
- 支持多种权限等级的复杂场景

### **4. 管理友好**
- 详细的日志记录便于管理员监控
- 自动化处理，无需手动干预
- 配置灵活，可调整重置时间

现在搜刮箱支持超额完成检测，当玩家权限降级时会自动处理并显示适当的提示和倒计时！

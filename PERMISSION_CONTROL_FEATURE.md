# 权限控制物品数量功能

## ✅ 已完成的功能

### 🎯 **功能描述**
根据玩家的权限等级，显示不同数量的随机物品，让VIP玩家获得更多的搜刮奖励。

### 🔧 **实现内容**

#### 1. 配置文件权限设置 ✅

**配置路径**: `src/main/resources/config.yml`

```yaml
# GUI设置
gui:
  # GUI标题
  title: "&6&l搜刮奖励"
  # GUI大小 (9, 18, 27, 36, 45, 54)
  size: 27
  # 默认随机显示的物品数量 (1-27) - 当玩家没有特殊权限时使用
  random-items: 5
  
  # 权限控制的物品数量设置
  permission-items:
    # VIP权限 - 更多物品
    "scavenge.vip":
      random-items: 8
      display-name: "&6&lVIP搜刮奖励"
    # SVIP权限 - 更多物品
    "scavenge.svip": 
      random-items: 12
      display-name: "&e&lSVIP搜刮奖励"
    # MVP权限 - 最多物品
    "scavenge.mvp":
      random-items: 15
      display-name: "&c&lMVP搜刮奖励"
    # 管理员权限 - 满箱物品
    "scavenge.admin":
      random-items: 20
      display-name: "&4&l管理员搜刮奖励"
```

#### 2. ScavengeChest权限检测 ✅

**文件**: `src/main/java/com/scavenge/ScavengeChest.java`

**新增方法**:
```java
/**
 * 根据玩家权限获取随机物品数量
 */
private int getRandomItemsForPlayer(Player player) {
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    
    if (player == null) {
        // 如果没有玩家信息，使用默认数量
        return plugin.getConfig().getInt("gui.random-items", 5);
    }

    // 检查权限配置，从高级权限到低级权限
    String[] permissions = {
        "scavenge.admin",
        "scavenge.mvp", 
        "scavenge.svip",
        "scavenge.vip"
    };

    for (String permission : permissions) {
        if (player.hasPermission(permission)) {
            String path = "gui.permission-items." + permission + ".random-items";
            int items = plugin.getConfig().getInt(path, -1);
            if (items > 0) {
                return items;
            }
        }
    }

    // 如果没有特殊权限，使用默认数量
    return plugin.getConfig().getInt("gui.random-items", 5);
}
```

**修改的方法**:
```java
/**
 * 初始化搜刮箱内容
 */
private void initializeItems(Player player) {
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    int randomItems = getRandomItemsForPlayer(player); // 根据权限获取物品数量
    
    // ... 其余初始化逻辑
}

/**
 * 根据玩家权限重置搜刮箱
 */
public void resetWithPlayer(Player player) {
    this.lastResetTime = System.currentTimeMillis();
    this.isActive = false;
    this.activePlayer = null;
    initializeItems(player); // 传入玩家参数

    // 移除全息图
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    if (plugin != null && plugin.getHologramManager() != null) {
        plugin.getHologramManager().removeHologram(location);
    }
}
```

#### 3. ScavengeChestGUI权限标题 ✅

**文件**: `src/main/java/com/scavenge/ScavengeChestGUI.java`

**新增方法**:
```java
/**
 * 根据玩家权限获取GUI标题
 */
private String getGUITitleForPlayer(Player player) {
    // 检查权限配置，从高级权限到低级权限
    String[] permissions = {
        "scavenge.admin",
        "scavenge.mvp", 
        "scavenge.svip",
        "scavenge.vip"
    };

    for (String permission : permissions) {
        if (player.hasPermission(permission)) {
            String path = "gui.permission-items." + permission + ".display-name";
            String title = plugin.getConfig().getString(path, null);
            if (title != null) {
                return title.replace("&", "§");
            }
        }
    }

    // 如果没有特殊权限，使用默认标题
    return plugin.getConfig().getString("gui.title", "&6搜刮奖励")
            .replace("&", "§");
}
```

**修改的构造函数**:
```java
public ScavengeChestGUI(ScavengePlugin plugin, Player player, ScavengeChest scavengeChest) {
    // ...
    String title = getGUITitleForPlayer(player); // 根据权限获取标题
    int size = plugin.getConfig().getInt("gui.size", 27);
    
    this.inventory = Bukkit.createInventory(null, size, title);
    // ...
}
```

**修改的setupGUI方法**:
```java
private void setupGUI() {
    // 填充背景
    fillBackground();

    // 加载搜刮箱的当前状态
    loadChestState();

    // 设置搜刮箱为活跃状态
    scavengeChest.setActive(true);
    scavengeChest.setActivePlayer(player.getUniqueId());

    // 如果有未搜索的物品，开始搜索
    if (!scavengeChest.getUnSearchedSlots().isEmpty()) {
        startNextSearch();
    }
}
```

## 🎯 **权限等级设置**

### 权限节点
- `scavenge.vip` - VIP权限
- `scavenge.svip` - SVIP权限  
- `scavenge.mvp` - MVP权限
- `scavenge.admin` - 管理员权限

### 物品数量对比
| 权限等级 | 权限节点 | 物品数量 | GUI标题 |
|---------|---------|---------|---------|
| 普通玩家 | 无 | 5个 | `&6&l搜刮奖励` |
| VIP | `scavenge.vip` | 8个 | `&6&lVIP搜刮奖励` |
| SVIP | `scavenge.svip` | 12个 | `&e&lSVIP搜刮奖励` |
| MVP | `scavenge.mvp` | 15个 | `&c&lMVP搜刮奖励` |
| 管理员 | `scavenge.admin` | 20个 | `&4&l管理员搜刮奖励` |

### 权限检查优先级
系统按照以下顺序检查权限（从高到低）：
1. `scavenge.admin` (最高优先级)
2. `scavenge.mvp`
3. `scavenge.svip`
4. `scavenge.vip`
5. 默认设置 (无特殊权限)

## 🔧 **使用方法**

### 1. 服务器管理员设置权限
```bash
# 给玩家VIP权限
/lp user <玩家名> permission set scavenge.vip true

# 给玩家SVIP权限
/lp user <玩家名> permission set scavenge.svip true

# 给玩家MVP权限
/lp user <玩家名> permission set scavenge.mvp true

# 给玩家管理员权限
/lp user <玩家名> permission set scavenge.admin true
```

### 2. 权限组设置
```bash
# 创建VIP组并设置权限
/lp creategroup vip
/lp group vip permission set scavenge.vip true

# 创建SVIP组并设置权限
/lp creategroup svip
/lp group svip permission set scavenge.svip true

# 创建MVP组并设置权限
/lp creategroup mvp
/lp group mvp permission set scavenge.mvp true
```

### 3. 将玩家添加到权限组
```bash
# 将玩家添加到VIP组
/lp user <玩家名> parent add vip

# 将玩家添加到SVIP组
/lp user <玩家名> parent add svip

# 将玩家添加到MVP组
/lp user <玩家名> parent add mvp
```

## 🎮 **游戏体验**

### 普通玩家 (5个物品)
- 基础搜刮体验
- 标准奖励数量
- 默认GUI标题

### VIP玩家 (8个物品)
- 60%更多物品
- 专属VIP标题
- 更高获得奖励的机会

### SVIP玩家 (12个物品)
- 140%更多物品
- 专属SVIP标题
- 显著提升的奖励获得率

### MVP玩家 (15个物品)
- 200%更多物品
- 专属MVP标题
- 大幅提升的奖励获得率

### 管理员 (20个物品)
- 300%更多物品
- 专属管理员标题
- 最高的奖励获得率

## ⚙️ **配置自定义**

### 修改物品数量
在 `config.yml` 中修改对应权限的 `random-items` 值：
```yaml
permission-items:
  "scavenge.vip":
    random-items: 10  # 修改VIP物品数量为10个
```

### 修改GUI标题
在 `config.yml` 中修改对应权限的 `display-name` 值：
```yaml
permission-items:
  "scavenge.vip":
    display-name: "&6&l尊贵VIP搜刮"  # 自定义VIP标题
```

### 添加新权限等级
```yaml
permission-items:
  "scavenge.premium":
    random-items: 25
    display-name: "&d&l至尊搜刮奖励"
```

## 🔄 **工作流程**

1. **玩家打开搜刮箱**
2. **系统检查玩家权限** (从高到低)
3. **根据权限设置物品数量**
4. **显示对应的GUI标题**
5. **生成相应数量的随机奖励**
6. **玩家享受权限对应的搜刮体验**

## 🎯 **优势特点**

### 1. 权限分级明确
- 清晰的权限等级划分
- 递增的奖励数量
- 专属的GUI标题

### 2. 配置灵活
- 可自定义物品数量
- 可自定义GUI标题
- 可添加新权限等级

### 3. 兼容性好
- 兼容现有搜刮系统
- 不影响其他功能
- 向下兼容普通玩家

### 4. 激励机制
- 鼓励玩家购买VIP
- 提升服务器收入
- 增强玩家粘性

## 🚀 **总结**

权限控制物品数量功能成功实现了：

1. ✅ **配置文件权限设置** - 支持多级权限配置
2. ✅ **动态物品数量** - 根据权限自动调整物品数量
3. ✅ **专属GUI标题** - 不同权限显示不同标题
4. ✅ **权限检查机制** - 从高到低的权限优先级
5. ✅ **向下兼容** - 普通玩家正常使用默认设置

### 权限效果对比：
- **普通玩家**: 5个物品，基础体验
- **VIP玩家**: 8个物品，提升60%
- **SVIP玩家**: 12个物品，提升140%  
- **MVP玩家**: 15个物品，提升200%
- **管理员**: 20个物品，提升300%

现在不同权限的玩家将获得不同数量的搜刮物品，有效提升了VIP玩家的游戏体验，同时为服务器提供了良好的收费激励机制！

# 指令物品收集任务使用指南

## 🎯 功能概述

指令物品收集任务允许你创建需要玩家收集特定物品的任务，这些物品可以通过指令给予、从其他插件获得、或者从搜刮箱中获得。

## 📝 配置格式

### 基础配置
```yaml
quest_id:
  name: "任务名称"
  description: "任务描述"
  type: "DAILY"  # DAILY/WEEKLY/SPECIAL
  goal: "COLLECT_COMMAND_ITEM"  # 新的任务目标类型
  target-amount: 5  # 需要收集的数量
  target-material: "DIAMOND"  # 目标物品的材质
  target-display-name: null  # 目标物品的显示名称（可选）
  rewards:
    - "give {player} emerald_block 2"
  console: true
  rare: false
```

### 配置参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `goal` | String | ✅ | 必须设置为 `COLLECT_COMMAND_ITEM` |
| `target-material` | String | ✅ | 目标物品的Minecraft材质名称 |
| `target-display-name` | String | ❌ | 物品的显示名称，null表示任意显示名称 |
| `target-amount` | Integer | ✅ | 需要收集的物品数量 |

## 🎮 使用示例

### 示例1：收集钻石
```yaml
daily_collect_diamonds:
  name: "钻石收集者"
  description: "收集5个钻石"
  type: "DAILY"
  goal: "COLLECT_COMMAND_ITEM"
  target-amount: 5
  target-material: "DIAMOND"
  target-display-name: null  # 任意钻石都可以
  rewards:
    - "give {player} emerald_block 2"
    - "give {player} experience_bottle 10"
  console: true
  rare: false
```

**触发方式：**
- 管理员执行：`/give 玩家名 diamond 5`
- 玩家从搜刮箱获得钻石
- 其他插件给予钻石

### 示例2：收集特定名称的剑
```yaml
weekly_collect_special_sword:
  name: "传说武器收集者"
  description: "收集1把传说之剑"
  type: "WEEKLY"
  goal: "COLLECT_COMMAND_ITEM"
  target-amount: 1
  target-material: "DIAMOND_SWORD"
  target-display-name: "传说之剑"  # 必须包含此显示名称
  rewards:
    - "give {player} diamond_block 5"
    - "give {player} experience_bottle 20"
  console: true
  rare: true
```

**触发方式：**
- 管理员执行：`/give 玩家名 diamond_sword{display:{Name:'"传说之剑"'}} 1`
- 使用其他插件给予带有"传说之剑"显示名称的钻石剑

### 示例3：收集自定义物品
```yaml
special_collect_magic_apple:
  name: "魔法苹果猎人"
  description: "收集3个魔法苹果"
  type: "SPECIAL"
  goal: "COLLECT_COMMAND_ITEM"
  target-amount: 3
  target-material: "GOLDEN_APPLE"
  target-display-name: "§6魔法苹果"
  rewards:
    - "give {player} diamond_block 10"
    - "give {player} experience_bottle 50"
  console: true
  rare: true
```

## 🔧 工作原理

### 检测机制
插件通过以下事件监听玩家获得物品：

1. **PlayerPickupItemEvent** - 玩家拾取掉落物品
2. **InventoryClickEvent** - 玩家点击背包（检测指令给予的物品）

### 匹配逻辑
1. **材质匹配**：检查物品材质是否与 `target-material` 匹配
2. **显示名称匹配**：
   - 如果 `target-display-name` 为 `null`，则任意显示名称都匹配
   - 如果指定了显示名称，则物品的显示名称必须包含指定文本

### 进度更新
- 每次获得匹配物品时，任务进度增加物品数量
- 达到目标数量时，任务自动完成
- 玩家收到任务完成通知

## 🎯 常见用法场景

### 1. 经济系统集成
```yaml
daily_collect_money:
  name: "财富积累者"
  description: "收集1000金币"
  goal: "COLLECT_COMMAND_ITEM"
  target-material: "GOLD_INGOT"
  target-display-name: "金币"
  target-amount: 1000
```

### 2. 副本奖励收集
```yaml
weekly_collect_dungeon_key:
  name: "地牢探索者"
  description: "收集5把地牢钥匙"
  goal: "COLLECT_COMMAND_ITEM"
  target-material: "TRIPWIRE_HOOK"
  target-display-name: "地牢钥匙"
  target-amount: 5
```

### 3. 活动物品收集
```yaml
special_collect_event_token:
  name: "活动参与者"
  description: "收集10个活动代币"
  goal: "COLLECT_COMMAND_ITEM"
  target-material: "EMERALD"
  target-display-name: "活动代币"
  target-amount: 10
```

## ⚠️ 注意事项

### 1. 材质名称
- 使用正确的Minecraft材质名称（如 `DIAMOND`, `DIAMOND_SWORD`）
- 材质名称不区分大小写
- 可以在 [Minecraft Wiki](https://minecraft.wiki/w/Java_Edition_data_values) 查找正确的材质名称

### 2. 显示名称匹配
- 显示名称匹配使用 `contains()` 方法，支持部分匹配
- 支持颜色代码（如 `§6魔法苹果`）
- 设置为 `null` 表示不检查显示名称

### 3. 性能考虑
- 插件会监听所有物品获得事件，但只处理相关任务
- 建议不要创建过多的物品收集任务

### 4. 兼容性
- 与其他插件的物品给予系统兼容
- 支持通过指令、插件、搜刮箱等方式获得的物品

## 🔄 与其他任务类型的区别

| 任务类型 | 触发条件 | 用途 |
|----------|----------|------|
| `SCAVENGE_COUNT` | 完成搜刮操作 | 鼓励使用搜刮功能 |
| `FIND_RARE_ITEMS` | 发现稀有物品 | 鼓励寻找稀有奖励 |
| `COMPLETE_CHESTS` | 完成搜刮箱 | 鼓励完整搜刮 |
| `COLLECT_SPECIFIC` | 收集特定物品 | 通用物品收集 |
| `COLLECT_COMMAND_ITEM` | 收集指令给予的物品 | 与其他系统集成 |

## 🎮 玩家体验

### 任务显示
- 在任务GUI中显示为"收集指令物品"
- 显示当前进度和目标数量
- 完成后可以领取奖励

### 进度追踪
- 实时更新任务进度
- 支持批量物品获得
- 自动检测任务完成

这个功能让你的搜刮插件可以与服务器的其他系统完美集成，创造更丰富的游戏体验！

# 全息图权限检测和物品数量显示修复

## 🔍 **用户需求**

用户希望：
> "全息要自动检测降级和升级显示的搜刮物品数量 确保都搜刮完就进入重置时间倒计时 还要检测每个权限和默认权限的搜刮显示"

## 🎯 **修复目标**

1. **检测当前开箱玩家权限** - 不是附近玩家，而是正在开箱的玩家
2. **动态显示物品数量** - 根据玩家权限显示对应的总物品数量
3. **智能完成判断** - 根据当前玩家权限判断是否真正完成
4. **权限升级/降级适配** - 全息图自动适配权限变化

## 🔧 **修复内容**

### **1. 修改全息图权限检测逻辑**

#### **修复前的问题**
```java
// 错误：检测附近最高权限玩家
Player currentPlayer = getNearestPlayerWithPermission(chestLocation);
int totalItems = getTotalItemsForPlayer(currentPlayer);
```

#### **修复后的正确逻辑**
```java
// 正确：检测当前开箱玩家权限
Player currentPlayer = getCurrentChestPlayer(chest);
int totalItems = getTotalItemsForPlayer(currentPlayer);

plugin.getLogger().info("=== 全息图权限检测 ===");
plugin.getLogger().info("当前开箱玩家: " + (currentPlayer != null ? currentPlayer.getName() : "无玩家"));
plugin.getLogger().info("玩家权限物品数量: " + totalItems);
plugin.getLogger().info("已探索物品: " + exploredItems);
plugin.getLogger().info("已领取物品: " + claimedItems);
```

### **2. 添加当前开箱玩家检测方法**

#### **getCurrentChestPlayer() 方法**
```java
private Player getCurrentChestPlayer(ScavengeChest chest) {
    // 1. 优先检查搜刮箱的活跃玩家
    if (chest.getActivePlayer() != null) {
        Player activePlayer = Bukkit.getPlayer(chest.getActivePlayer());
        if (activePlayer != null && activePlayer.isOnline()) {
            return activePlayer;
        }
    }

    // 2. 检查附近是否有玩家正在查看搜刮箱GUI
    Location chestLocation = chest.getLocation();
    double viewDistance = plugin.getConfig().getDouble("scavenge-chest.hologram.view-distance", 16.0);
    
    for (Player player : Bukkit.getOnlinePlayers()) {
        if (player.getWorld().equals(chestLocation.getWorld()) &&
                player.getLocation().distance(chestLocation) <= viewDistance) {
            
            // 检查玩家是否正在查看搜刮箱GUI
            if (player.getOpenInventory() != null && 
                player.getOpenInventory().getTitle().contains("搜刮箱")) {
                return player;
            }
        }
    }

    // 3. 如果都没有，返回附近第一个玩家作为参考
    for (Player player : Bukkit.getOnlinePlayers()) {
        if (player.getWorld().equals(chestLocation.getWorld()) &&
                player.getLocation().distance(chestLocation) <= viewDistance) {
            return player;
        }
    }

    return null;
}
```

### **3. 智能完成判断逻辑**

#### **根据当前玩家权限判断完成状态**
```java
// 检查是否根据当前权限已经完成搜刮
boolean isCompletedForCurrentPermission = (exploredItems >= totalItems);

// 如果根据当前权限已完成但还没设置重置时间，设置重置时间
if (isCompletedForCurrentPermission && chest.getLastResetTime() == 0) {
    chest.setLastResetTime(System.currentTimeMillis());
    plugin.getLogger().info("全息图检测到权限完成，设置重置时间: " + totalItems + " 物品已探索完成");
}

// 检查是否应该显示重置倒计时
if (chest.shouldShowResetCountdown() || (isCompletedForCurrentPermission && chest.getLastResetTime() > 0)) {
    // 切换到倒计时模式
    long resetTime = chest.getLastResetTime() + (plugin.getConfig().getInt("scavenge-chest.reset-time", 300) * 1000L);
    this.cancel();
    updateTasks.remove(chestLocation);
    createCountdownHologram(chestLocation, resetTime);
    return;
}
```

### **4. 动态显示逻辑**

#### **根据权限显示不同状态**
```java
// 显示进度信息
String displayText;
if (isCompletedForCurrentPermission) {
    // 根据当前权限已完成，显示完成状态
    displayText = String.format("&a&l已完成 &f%d/%d &7(已领取: %d)",
            exploredItems, totalItems, claimedItems);
} else {
    // 还在探索中，显示探索进度
    displayText = String.format("&e&l探索中 &f%d/%d &7(已领取: %d)",
            exploredItems, totalItems, claimedItems);
}

hologram.setCustomName(displayText.replace("&", "§"));
```

### **5. 权限等级检测方法**

#### **getPlayerPermissionLevel() 方法**
```java
private int getPlayerPermissionLevel(Player player) {
    if (player.hasPermission("scavenge.admin")) return 4;
    if (player.hasPermission("scavenge.mvp")) return 3;
    if (player.hasPermission("scavenge.svip")) return 2;
    if (player.hasPermission("scavenge.vip")) return 1;
    return 0; // 默认权限
}
```

#### **getTotalItemsForPlayer() 方法**
```java
private int getTotalItemsForPlayer(Player player) {
    if (player == null) {
        return plugin.getConfig().getInt("gui.random-items", 5);
    }

    // 检查权限配置，从高级权限到低级权限
    String[] permissions = {
            "scavenge.admin",
            "scavenge.mvp", 
            "scavenge.svip",
            "scavenge.vip"
    };

    for (String permission : permissions) {
        if (player.hasPermission(permission)) {
            String path = "gui.permission-items." + permission + ".random-items";
            int items = plugin.getConfig().getInt(path, -1);
            if (items > 0) {
                return items;
            }
        }
    }

    // 如果没有特殊权限，使用默认数量
    return plugin.getConfig().getInt("gui.random-items", 5);
}
```

## 📊 **不同权限下的表现**

### **权限配置示例**
```yaml
gui:
  random-items: 5  # 默认权限物品数量
  permission-items:
    scavenge.vip:
      random-items: 8
    scavenge.svip:
      random-items: 12
    scavenge.mvp:
      random-items: 16
    scavenge.admin:
      random-items: 20
```

### **全息图显示效果**

#### **默认权限玩家 (5个物品)**
- **探索中**: `§e§l探索中 §f3/5 §7(已领取: 1)`
- **已完成**: `§a§l已完成 §f5/5 §7(已领取: 3)`
- **重置倒计时**: `§c§l重置倒计时: §f4分32秒`

#### **VIP权限玩家 (8个物品)**
- **探索中**: `§e§l探索中 §f6/8 §7(已领取: 2)`
- **已完成**: `§a§l已完成 §f8/8 §7(已领取: 5)`
- **重置倒计时**: `§c§l重置倒计时: §f4分32秒`

#### **ADMIN权限玩家 (20个物品)**
- **探索中**: `§e§l探索中 §f15/20 §7(已领取: 8)`
- **已完成**: `§a§l已完成 §f20/20 §7(已领取: 12)`
- **重置倒计时**: `§c§l重置倒计时: §f4分32秒`

## 🔄 **权限变化适配流程**

### **权限升级场景**
1. **VIP玩家升级为ADMIN** → 物品数量从8变为20
2. **全息图检测** → 显示"探索中 8/20 (已领取: X)"
3. **继续探索** → 可以探索更多物品直到20个
4. **完成判断** → 探索到20个才显示完成

### **权限降级场景**
1. **ADMIN玩家降级为VIP** → 物品数量从20变为8
2. **已探索15个物品** → 超过VIP限制的8个
3. **超额完成检测** → 设置超额完成状态和重置时间
4. **全息图显示** → "重置倒计时: X分X秒"
5. **重置后** → 按VIP权限显示8个物品

### **实时检测机制**
```java
// 每秒检测一次当前开箱玩家权限
Player currentPlayer = getCurrentChestPlayer(chest);
int totalItems = getTotalItemsForPlayer(currentPlayer);

// 根据当前权限判断完成状态
boolean isCompletedForCurrentPermission = (exploredItems >= totalItems);

// 超额完成检测
if (exploredItems > totalItems) {
    if (!chest.isOverCompleted()) {
        chest.setOverCompleted(true);
        if (chest.getLastResetTime() == 0) {
            chest.setLastResetTime(System.currentTimeMillis());
        }
    }
}
```

## 🎮 **用户体验改善**

### **修复前的问题**
- ❌ 全息图显示固定的物品数量，不根据玩家权限变化
- ❌ 权限升级后无法探索更多物品
- ❌ 权限降级后不会自动进入重置倒计时
- ❌ 完成判断不准确

### **修复后的改善**
- ✅ 全息图动态显示当前玩家权限对应的物品数量
- ✅ 权限升级后可以继续探索更多物品
- ✅ 权限降级后自动检测超额完成并进入倒计时
- ✅ 根据当前权限准确判断完成状态

## 🛡️ **关键特性**

### **1. 实时权限检测**
- 每秒检测当前开箱玩家的权限
- 根据权限动态调整显示的总物品数量
- 支持权限升级和降级的实时适配

### **2. 智能完成判断**
- 不再使用固定的完成条件
- 根据当前玩家权限判断是否真正完成
- 支持权限变化后的完成状态重新计算

### **3. 超额完成处理**
- 自动检测权限降级导致的超额完成
- 立即设置重置时间并显示倒计时
- 确保系统状态的一致性

### **4. 调试信息输出**
- 详细的日志输出便于调试
- 显示当前检测到的玩家和权限信息
- 记录完成状态变化和重置时间设置

## 💡 **技术要点**

### **玩家检测优先级**
1. **搜刮箱活跃玩家** - 最高优先级
2. **正在查看GUI的玩家** - 中等优先级  
3. **附近的玩家** - 最低优先级

### **权限检测机制**
- 从高级权限到低级权限依次检查
- 使用配置文件中的权限物品数量
- 支持默认权限的回退机制

### **状态同步**
- 全息图与搜刮箱状态实时同步
- 权限变化立即反映在显示中
- 完成状态根据当前权限动态计算

### **性能优化**
- 每秒更新一次，避免过度计算
- 缓存权限检测结果
- 及时清理无效的全息图和任务

现在全息图能够智能检测当前开箱玩家的权限，动态显示对应的物品数量，并根据权限变化自动适配完成状态！

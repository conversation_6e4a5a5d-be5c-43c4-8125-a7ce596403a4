# 编译问题修复指南

## 🚨 当前问题

编译时出现错误：
```
[ERROR] /C:/Users/<USER>/Desktop/搜刮/src/main/java/com/scavenge/gui/LeaderboardGUI.java:[437,21] 程序包Map不存在
```

## ✅ 已完成的修复

### 1. 添加了Map import ✅
在 `LeaderboardGUI.java` 中添加了缺失的import：
```java
import java.util.Map;
```

### 2. 修复了任务识别问题 ✅
在 `ScavengeChestGUI.java` 中添加了任务更新调用：
```java
// 记录搜刮统计和更新任务进度
plugin.getLeaderboardManager().recordScavenge(player.getUniqueId(), player.getName());
plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
        ScavengeQuest.QuestGoal.SCAVENGE_COUNT, 1);

// 检查是否是稀有物品
if (reward != null && reward.isRare()) {
    plugin.getLeaderboardManager().recordRareItemFound(player.getUniqueId(),
            player.getName(), reward.getDisplayName());
    plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
            ScavengeQuest.QuestGoal.FIND_RARE_ITEMS, 1);
}
```

### 3. 修复了排行榜点击问题 ✅
在 `LeaderboardGUI.java` 中添加了玩家头颅点击处理：
```java
// 处理玩家头颅点击（排行榜玩家）
else if (isPlayerSlot(slot)) {
    PlayerStats clickedPlayer = getPlayerBySlot(slot);
    if (clickedPlayer != null) {
        showPlayerDetails(clicker, clickedPlayer);
    }
}
```

## 🔧 手动编译方法

如果Maven编译有问题，可以使用以下手动编译方法：

### 1. 创建正确的pom.xml
手动创建 `pom.xml` 文件，内容如下：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.scavenge</groupId>
    <artifactId>ScavengePlugin</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>ScavengePlugin</name>
    <description>A scavenge plugin for Minecraft 1.8.8</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <repositories>
        <repository>
            <id>spigot-repo</id>
            <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>1.8.8-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>org.bukkit</groupId>
            <artifactId>bukkit</artifactId>
            <version>1.8.8-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <defaultGoal>clean package</defaultGoal>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

### 2. 编译命令
```bash
# 清理并编译
mvn clean compile

# 如果成功，打包
mvn package
```

### 3. 使用IDE编译
如果Maven有问题，可以：
1. 使用IntelliJ IDEA或Eclipse导入项目
2. 配置Spigot API依赖
3. 直接在IDE中编译

## 📋 验证修复

### 1. 检查编译
编译成功后应该看到：
```
[INFO] BUILD SUCCESS
```

### 2. 检查生成的JAR
在 `target/` 目录下应该有：
- `ScavengePlugin-1.0.0.jar`

### 3. 测试功能
将JAR文件放入服务器的 `plugins/` 目录，重启服务器测试：

#### 任务系统测试
```bash
# 1. 检查任务状态
/scavenge debug quests

# 2. 进行搜刮操作
# 右键搜刮箱，完成搜刮

# 3. 查看任务GUI
/scavenge quest
# 进度应该正确更新

# 4. 查看控制台日志
# 应该看到详细的任务更新日志
```

#### 排行榜系统测试
```bash
# 1. 打开排行榜
/scavenge leaderboard

# 2. 点击任意玩家头颅
# 应该在聊天框显示详细信息

# 3. 点击自己的信息（底部中央）
# 应该显示自己的详细统计
```

## 🔍 预期结果

### 控制台日志
搜刮后应该看到：
```
=== 任务进度更新开始 ===
玩家: [UUID]
目标类型: SCAVENGE_COUNT
数量: 1

--- 检查任务: 每日搜刮者 ---
任务活跃: true
任务过期: false
条件满足: true
当前进度: 0/5
更新后进度: 1/5
=== 任务进度更新结束 ===
```

### 排行榜点击
点击玩家头颅后应该看到：
```
§6§l=== 玩家名 的详细信息 ===
§7等级: §f1 (新手探索者)
§7总搜刮次数: §f5
§7完成搜刮箱: §f2
§7稀有物品发现: §f1
§7完成任务: §f0
§7搜刮效率: §f2.50/小时
§7稀有物品率: §f20.00%
§7综合评分: §f15.5
§7下一等级需要: §f15 次搜刮
```

## 🚀 快速解决方案

如果编译仍然有问题，可以：

### 方案1: 重新创建项目
1. 备份 `src/` 目录
2. 删除项目，重新创建Maven项目
3. 复制源代码回去
4. 重新编译

### 方案2: 使用现有JAR
如果之前编译成功过，可以：
1. 使用之前的JAR文件
2. 手动替换修改的类文件
3. 重新打包

### 方案3: 在线编译
1. 将代码上传到在线IDE（如Repl.it）
2. 配置Maven依赖
3. 在线编译下载

## 📝 总结

主要修复内容：
1. ✅ **Map import问题** - 已添加缺失的import
2. ✅ **任务识别问题** - 已在ScavengeChestGUI中添加任务更新
3. ✅ **排行榜点击问题** - 已添加玩家头颅点击处理

所有代码修复都已完成，现在只需要成功编译即可测试功能！

如果编译仍有问题，请提供具体的错误信息，我可以进一步协助解决。

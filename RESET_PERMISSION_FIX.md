# 搜刮箱重置权限检测修复说明

## 🔍 **问题分析**

### **问题现象**
使用 `/scavenge reset all` 重置所有搜刮箱后，玩家打开搜刮箱时显示默认的5个物品，而不是根据玩家权限显示对应数量的物品。

### **问题原因**
1. **重置方法缺陷**: `reset()` 方法没有重置 `hasBeenInitializedWithPlayer` 标志
2. **初始化逻辑问题**: 玩家打开搜刮箱时的检查条件不完整
3. **权限检测失效**: 重置后的搜刮箱无法触发权限重新检测

## 🔧 **修复方案**

### **修复ScavengeChest.java - reset()方法**

#### **修复前**
```java
public void reset() {
    this.lastResetTime = System.currentTimeMillis();
    this.isActive = false;
    this.activePlayer = null;
    initializeItems(null); // 使用默认数量，没有重置权限标志
    
    // 移除全息图
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    if (plugin != null && plugin.getHologramManager() != null) {
        plugin.getHologramManager().removeHologram(location);
    }
}
```

#### **修复后**
```java
public void reset() {
    this.lastResetTime = System.currentTimeMillis();
    this.isActive = false;
    this.activePlayer = null;
    this.hasBeenInitializedWithPlayer = false; // 重置玩家初始化标志，下次打开时会根据玩家权限重新初始化
    initializeItems(null);
    
    // 移除全息图
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    if (plugin != null && plugin.getHologramManager() != null) {
        plugin.getHologramManager().removeHologram(location);
    }
}
```

### **修复逻辑说明**

#### **权限检测流程**
1. **管理员重置**: `/scavenge reset all` → `chest.reset()` → `hasBeenInitializedWithPlayer = false`
2. **玩家打开**: 玩家右键搜刮箱 → `ScavengeChestGUI` 构造
3. **权限检查**: `setupGUI()` → `needsPlayerInitialization()` → 返回 `true`
4. **重新初始化**: `resetWithPlayer(player)` → 根据玩家权限设置物品数量

#### **关键检查条件**
```java
// ScavengeChestGUI.setupGUI()
if (scavengeChest.needsPlayerInitialization() || isChestEmpty()) {
    scavengeChest.resetWithPlayer(player);
}

// ScavengeChest.needsPlayerInitialization()
public boolean needsPlayerInitialization() {
    return !hasBeenInitializedWithPlayer; // 现在会返回true
}
```

## 🎯 **修复前后对比**

### **修复前的流程**
1. **管理员重置**: `reset()` → 使用默认5个物品 → `hasBeenInitializedWithPlayer` 保持原值
2. **玩家打开**: `needsPlayerInitialization()` → 可能返回 `false`
3. **结果**: 不触发权限重新检测，显示默认5个物品

### **修复后的流程**
1. **管理员重置**: `reset()` → 使用默认5个物品 → `hasBeenInitializedWithPlayer = false`
2. **玩家打开**: `needsPlayerInitialization()` → 返回 `true`
3. **权限检测**: `resetWithPlayer(player)` → 根据玩家权限重新初始化
4. **结果**: 显示正确数量的物品

## 🎮 **测试场景**

### **场景1：VIP玩家测试**
1. 给玩家VIP权限：`/lp user <玩家名> permission set scavenge.vip true`
2. 管理员执行：`/scavenge reset all`
3. VIP玩家打开搜刮箱
4. **预期结果**: 显示VIP配置的物品数量（如10个），而不是默认的5个

### **场景2：普通玩家测试**
1. 普通玩家（无特殊权限）
2. 管理员执行：`/scavenge reset all`
3. 普通玩家打开搜刮箱
4. **预期结果**: 显示默认的5个物品

### **场景3：不同权限玩家测试**
1. 设置不同权限的玩家：
   - 玩家A：`scavenge.admin` (20个物品)
   - 玩家B：`scavenge.mvp` (15个物品)
   - 玩家C：`scavenge.vip` (10个物品)
   - 玩家D：无权限 (5个物品)
2. 管理员执行：`/scavenge reset all`
3. 各玩家分别打开同一个搜刮箱
4. **预期结果**: 每个玩家看到的物品数量与其权限对应

### **场景4：重复重置测试**
1. 玩家打开搜刮箱（显示权限对应数量）
2. 管理员执行：`/scavenge reset all`
3. 同一玩家再次打开搜刮箱
4. **预期结果**: 仍然显示权限对应的数量，不会变成默认5个

## 🔄 **权限配置示例**

### **config.yml配置**
```yaml
gui:
  random-items: 5  # 默认物品数量
  permission-items:
    scavenge.admin:
      random-items: 20
      display-name: "&c&l管理员搜刮箱 &7(20个物品)"
    scavenge.mvp:
      random-items: 15
      display-name: "&6&lMVP搜刮箱 &7(15个物品)"
    scavenge.svip:
      random-items: 12
      display-name: "&d&lSVIP搜刮箱 &7(12个物品)"
    scavenge.vip:
      random-items: 10
      display-name: "&a&lVIP搜刮箱 &7(10个物品)"
```

### **权限设置命令**
```bash
# 给予VIP权限
/lp user <玩家名> permission set scavenge.vip true

# 给予SVIP权限
/lp user <玩家名> permission set scavenge.svip true

# 给予MVP权限
/lp user <玩家名> permission set scavenge.mvp true

# 给予管理员权限
/lp user <玩家名> permission set scavenge.admin true
```

## 📊 **技术细节**

### **关键变量状态**
- `hasBeenInitializedWithPlayer`: 标记是否已根据玩家权限初始化
- `unSearchedSlots`: 未搜索的槽位集合
- `items`: 搜刮箱中的物品映射

### **初始化时机**
1. **首次创建**: 搜刮箱放置时，使用默认配置
2. **玩家打开**: 检查是否需要根据玩家权限重新初始化
3. **管理员重置**: 清除权限标志，下次打开时重新检测

### **权限检查优先级**
1. `scavenge.admin` (最高优先级)
2. `scavenge.mvp`
3. `scavenge.svip`
4. `scavenge.vip`
5. 默认配置 (无特殊权限)

## 🚀 **优势**

### **1. 动态权限适应**
- 重置后自动根据玩家权限调整
- 不需要手动为每个权限组重置
- 支持权限变更后的自动更新

### **2. 管理便利性**
- 管理员一键重置所有搜刮箱
- 每个玩家看到适合其权限的内容
- 无需为不同权限组单独管理

### **3. 用户体验**
- VIP玩家获得更多搜刮机会
- 权限等级直观体现在搜刮数量上
- 重置后立即生效，无需额外操作

### **4. 系统稳定性**
- 权限检测逻辑清晰可靠
- 避免了权限配置不生效的问题
- 支持热重载和动态配置

现在 `/scavenge reset all` 命令会正确重置权限检测标志，玩家打开搜刮箱时会根据其权限显示对应数量的物品！

# 搜刮箱重置系统修复

## 🔍 **问题分析**

用户报告的问题：
1. **探索完成后全息图显示重置时间，但再次打开搜刮箱又可以探索**
2. **全息图重置时间消失，显示探索中**
3. **配置文件设置的重置时间到了箱子没有重置**
4. **需要确保重置时间到达时强制关闭玩家GUI并重置**

## 🔧 **修复方案**

### **问题1：倒计时结束后没有实际重置搜刮箱**

#### **修复前的错误逻辑**
```java
if (remainingTime <= 0) {
    // 倒计时结束，移除全息图
    removeHologram(chestLocation);
    this.cancel();
    return;
}
```
❌ **问题**: 只移除了全息图，没有重置搜刮箱

#### **修复后的正确逻辑**
```java
if (remainingTime <= 0) {
    // 倒计时结束，执行重置
    ScavengeChest chest = plugin.getScavengeChestManager().getChest(chestLocation);
    if (chest != null) {
        // 强制重置搜刮箱（包括关闭GUI）
        chest.reset();
        plugin.getLogger().info("搜刮箱自动重置: " + chestLocation.toString());
    }
    
    // 移除全息图和任务
    removeHologram(chestLocation);
    this.cancel();
    return;
}
```
✅ **修复**: 倒计时结束时实际重置搜刮箱

### **问题2：缺少全局重置检查机制**

#### **新增全局重置任务**
在 `ScavengePlugin.onEnable()` 中添加：
```java
// 启动全局重置检查任务
startGlobalResetTask();

private void startGlobalResetTask() {
    // 每30秒检查一次所有搜刮箱的重置时间
    Bukkit.getScheduler().runTaskTimer(this, () -> {
        if (scavengeChestManager != null) {
            scavengeChestManager.checkAllChestsForReset();
        }
    }, 600L, 600L); // 30秒 = 600 ticks
    
    getLogger().info("全局重置检查任务已启动 (每30秒检查一次)");
}
```

#### **新增检查方法**
在 `ScavengeChestManager` 中添加：
```java
public void checkAllChestsForReset() {
    int resetCount = 0;
    
    for (ScavengeChest chest : scavengeChests.values()) {
        if (chest.canReset()) {
            // 强制重置搜刮箱（包括关闭GUI）
            chest.reset();
            resetCount++;
            plugin.getLogger().info("搜刮箱自动重置: " + chest.getLocationKey());
        }
    }
    
    if (resetCount > 0) {
        plugin.getLogger().info("全局重置检查完成，重置了 " + resetCount + " 个搜刮箱");
    }
}
```

### **问题3：强制关闭GUI功能**

#### **强制关闭GUI方法**
在 `ScavengeChest.reset()` 中集成：
```java
public void reset() {
    // 强制关闭所有打开此搜刮箱的GUI
    forceCloseAllGUIs();
    
    this.lastResetTime = System.currentTimeMillis();
    this.isActive = false;
    this.activePlayer = null;
    this.hasBeenInitializedWithPlayer = false;
    this.isOverCompleted = false; // 重置超额完成状态
    initializeItems(null);

    // 移除全息图
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    if (plugin != null && plugin.getHologramManager() != null) {
        plugin.getHologramManager().removeHologram(location);
    }
}

private void forceCloseAllGUIs() {
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    if (plugin == null) return;
    
    // 获取所有在线玩家
    for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
        // 检查玩家是否打开了此搜刮箱的GUI
        ScavengeChestGUI activeGUI = ScavengeChestGUI.getActiveGUI(onlinePlayer);
        if (activeGUI != null && activeGUI.getScavengeChest().equals(this)) {
            // 关闭GUI并发送消息
            onlinePlayer.closeInventory();
            onlinePlayer.sendMessage(plugin.getMessage("force-reset-message"));
            plugin.getLogger().info("强制关闭玩家 " + onlinePlayer.getName() + " 的搜刮箱GUI (重置)");
        }
    }
}
```

## 🎯 **修复效果**

### **修复前的问题流程**
1. **玩家完成探索** → 全息图显示重置倒计时
2. **倒计时结束** → 只移除全息图，搜刮箱状态未重置
3. **玩家再次打开** → 可以继续探索（错误）
4. **全息图重新显示** → 显示探索中（错误）

### **修复后的正确流程**
1. **玩家完成探索** → 全息图显示重置倒计时
2. **倒计时结束** → 强制重置搜刮箱，关闭所有相关GUI
3. **玩家再次打开** → 显示全新的搜刮内容（正确）
4. **全息图显示** → 显示新的探索进度（正确）

## 🔄 **重置机制层级**

### **1. 倒计时全息图重置**
- **触发时机**: 倒计时到0时
- **执行内容**: 调用 `chest.reset()` 强制重置
- **适用场景**: 有倒计时全息图的情况

### **2. 全局重置检查**
- **触发时机**: 每30秒自动检查
- **执行内容**: 检查所有搜刮箱的 `canReset()` 状态
- **适用场景**: 确保没有遗漏的重置，兜底机制

### **3. 管理员指令重置**
- **触发时机**: 管理员执行重置命令
- **执行内容**: 立即调用 `chest.reset()` 强制重置
- **适用场景**: 手动管理和调试

## 📊 **预期日志输出**

### **自动重置日志**
```
[INFO] 搜刮箱自动重置: world:100:64:200
[INFO] 强制关闭玩家 PlayerName 的搜刮箱GUI (重置)
[INFO] 全局重置检查完成，重置了 3 个搜刮箱
```

### **倒计时重置日志**
```
[INFO] 搜刮箱自动重置: world:100:64:200
[INFO] 强制关闭玩家 PlayerName 的搜刮箱GUI (重置)
```

### **指令重置日志**
```
[INFO] 强制关闭玩家 PlayerName 的搜刮箱GUI (重置)
[INFO] 已重置 1 个搜刮箱!
```

## 🎮 **用户体验改善**

### **修复前的问题**
- ❌ 重置时间到了但箱子没重置
- ❌ 全息图显示错误信息
- ❌ 玩家可以无限探索同一个箱子
- ❌ 数据状态不一致

### **修复后的改善**
- ✅ 重置时间准确工作
- ✅ 全息图显示正确信息
- ✅ 强制关闭GUI确保数据一致性
- ✅ 多层重置机制确保可靠性

## 🛡️ **安全保障**

### **1. 多重检查机制**
- 倒计时全息图重置
- 全局定时检查重置
- 管理员手动重置
- 确保不会遗漏重置

### **2. 强制GUI关闭**
- 检测所有打开相关GUI的玩家
- 强制关闭GUI防止数据冲突
- 发送通知消息告知玩家
- 记录日志便于调试

### **3. 状态同步**
- 重置时清除所有相关状态
- 包括超额完成状态
- 确保下次使用时状态正确

## 🔧 **配置支持**

### **重置时间配置**
```yaml
scavenge-chest:
  reset-time: 300  # 5分钟 = 300秒
```

### **强制重置消息**
```yaml
messages:
  force-reset-message: "&c搜刮箱已重置，GUI已关闭。"
```

### **全息图配置**
```yaml
scavenge-chest:
  hologram:
    enabled: true
    update-interval: 1  # 每秒更新
    text-format: "&c&l重置倒计时: &f{time}"
```

## 🚀 **技术优势**

### **1. 可靠性**
- 多层重置机制确保不会遗漏
- 强制GUI关闭防止数据冲突
- 详细日志记录便于调试

### **2. 性能优化**
- 全局检查任务间隔合理（30秒）
- 只在需要时执行重置操作
- 避免不必要的资源消耗

### **3. 用户体验**
- 清晰的重置通知消息
- 准确的倒计时显示
- 无缝的状态转换

### **4. 管理友好**
- 详细的日志输出
- 支持手动重置命令
- 灵活的配置选项

现在搜刮箱重置系统应该完全正常工作，确保重置时间准确、GUI强制关闭、状态正确同步！

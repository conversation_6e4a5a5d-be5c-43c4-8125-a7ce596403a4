package com.scavenge;

import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

public class ScavengeManager {

    private final ScavengePlugin plugin;
    private List<ScavengeReward> allRewards;
    private Random random;

    public ScavengeManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.random = new Random();
        loadRewards();
    }

    public void reloadConfig() {
        loadRewards();
    }

    private void loadRewards() {
        allRewards = new ArrayList<>();

        ConfigurationSection section = plugin.getConfig().getConfigurationSection("rewards.items");

        if (section != null) {
            for (String key : section.getKeys(false)) {
                ConfigurationSection rewardSection = section.getConfigurationSection(key);
                if (rewardSection != null) {
                    ScavengeReward reward = loadReward(rewardSection);
                    if (reward != null) {
                        allRewards.add(reward);
                    } else {
                    }
                } else {
                }
            }
        } else {
        }

    }

    private ScavengeReward loadReward(ConfigurationSection section) {
        try {
            String type = section.getString("type", "ITEM").toUpperCase();
            double chance = section.getDouble("chance", 1.0);

            if ("COMMAND".equals(type)) {
                // 指令奖励
                String displayName = section.getString("display-name", "");
                String materialName = section.getString("display-material", "GOLD_NUGGET");

                Material displayMaterial = Material.valueOf(materialName.toUpperCase());

                List<String> commands = section.getStringList("commands");
                boolean console = section.getBoolean("console", false);
                int progressTime = section.getInt("progress-time", 3); // 默认3tick间隔

                boolean isRare = section.getBoolean("rare", chance <= 10.0); // 默认根据概率判断
                return new ScavengeReward(displayName, displayMaterial, chance, commands, console, progressTime,
                        isRare);
            } else {
                // 物品奖励
                String materialName = section.getString("material");
                if (materialName == null) {
                    return null;
                }

                Material material = Material.valueOf(materialName.toUpperCase());

                String amountStr = section.getString("amount", "1");
                int minAmount = 1, maxAmount = 1;

                if (amountStr.contains("-")) {
                    String[] parts = amountStr.split("-");
                    minAmount = Integer.parseInt(parts[0]);
                    maxAmount = Integer.parseInt(parts[1]);
                } else {
                    minAmount = maxAmount = Integer.parseInt(amountStr);
                }

                String displayName = section.getString("display-name", "");
                List<String> lore = section.getStringList("lore");
                List<String> enchantments = section.getStringList("enchantments");
                int progressTime = section.getInt("progress-time", 3); // 默认3tick间隔

                return new ScavengeReward(material, minAmount, maxAmount, displayName, lore, enchantments, chance,
                        progressTime);
            }
        } catch (Exception e) {
            return null;
        }
    }

    // 创建搜刮方块物品
    public ItemStack createScavengeBlock() {
        String materialName = plugin.getConfig().getString("scavenge-block.material", "CHEST");
        Material material = Material.valueOf(materialName);

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        String displayName = plugin.getConfig().getString("scavenge-block.display-name", "&6&l搜刮箱");
        meta.setDisplayName(displayName.replace("&", "§"));

        List<String> lore = plugin.getConfig().getStringList("scavenge-block.lore");
        List<String> coloredLore = new ArrayList<>();
        for (String line : lore) {
            coloredLore.add(line.replace("&", "§"));
        }
        meta.setLore(coloredLore);

        item.setItemMeta(meta);
        return item;
    }

    // 检查是否是搜刮方块
    public boolean isScavengeBlock(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();
        String expectedName = plugin.getConfig().getString("scavenge-block.display-name", "&6&l搜刮箱")
                .replace("&", "§");

        return meta.hasDisplayName() && meta.getDisplayName().equals(expectedName);
    }

    // 创建未搜索状态的物品
    public ItemStack createUnSearchedItem() {
        String materialName = plugin.getConfig().getString("gui.unsearched.material", "STAINED_GLASS_PANE");
        Material material = Material.valueOf(materialName);
        int data = plugin.getConfig().getInt("gui.unsearched.data", 7);

        ItemStack item = new ItemStack(material, 1, (short) data);
        ItemMeta meta = item.getItemMeta();

        String displayName = plugin.getConfig().getString("gui.unsearched.display-name", "&7&l未搜索")
                .replace("&", "§");
        meta.setDisplayName(displayName);

        item.setItemMeta(meta);
        return item;
    }

    // 创建搜索中状态的物品
    public ItemStack createSearchingItem(int progress) {
        String materialName = plugin.getConfig().getString("gui.searching.material", "STAINED_GLASS_PANE");
        Material material = Material.valueOf(materialName);
        int data = plugin.getConfig().getInt("gui.searching.data", 14);

        ItemStack item = new ItemStack(material, 1, (short) data);
        ItemMeta meta = item.getItemMeta();

        String displayName = plugin.getConfig().getString("gui.searching.display-name", "&c&l探索中 {progress}%")
                .replace("&", "§")
                .replace("{progress}", String.valueOf(progress));
        meta.setDisplayName(displayName);

        item.setItemMeta(meta);
        return item;
    }

    // 根据几率选择随机奖励
    public ScavengeReward selectRandomReward() {
        if (allRewards.isEmpty()) {
            return null;
        }

        // 计算总几率
        double totalChance = 0;
        for (ScavengeReward reward : allRewards) {
            totalChance += reward.getChance();
        }

        // 生成随机数
        double randomValue = random.nextDouble() * totalChance;

        // 选择奖励
        double currentChance = 0;
        for (ScavengeReward reward : allRewards) {
            currentChance += reward.getChance();
            if (randomValue <= currentChance) {
                return reward;
            }
        }

        // 如果没有选中任何奖励，返回第一个
        return allRewards.get(0);
    }

}

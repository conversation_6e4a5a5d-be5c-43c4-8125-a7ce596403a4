# 搜刮插件配置文件
# Scavenge Plugin Configuration

# 搜刮方块设置
scavenge-block:
  # 搜刮方块的材质类型 (CHEST, ENDER_CHEST, TRAPPED_CHEST)
  material: CHEST
  # 搜刮方块的显示名称
  display-name: "&6&l搜刮箱"
  # 搜刮方块的描述
  lore:
    - "&7右键放置后可以搜刮"
    - "&7每次搜刮都有不同的奖励"
    - "&e管理员专用物品"
  # 搜刮箱重置时间 (分钟)
  reset-time: 30
  # 搜刮箱冷却提示消息
  cooldown-message: "&c这个搜刮箱还需要等待 {time} 分钟才能重新搜刮!"
  # 搜刮箱可用提示消息
  available-message: "&a搜刮箱已重置，可以重新搜刮了!"

# GUI设置
gui:
  # GUI标题
  title: "&6&l搜刮奖励"
  # GUI大小 (9, 18, 27, 36, 45, 54)
  size: 27
  # 随机显示的物品数量 (1-27)
  random-items: 5

  # 未搜索状态的物品设置
  unsearched:
    material: STAINED_GLASS_PANE
    data: 7  # 灰色玻璃板
    display-name: "&7&l未搜索"
  # 搜索中状态的物品设置
  searching:
    material: STAINED_GLASS_PANE
    data: 14  # 红色玻璃板
    display-name: "&c&l探索中 {progress}%"
  # 搜索动画设置
  animation:
    # 搜索持续时间 (秒)
    duration: 3
    # 更新间隔 (tick, 20tick = 1秒) - 5tick确保稳定的一个个数字递减
    update-interval: 5
    # 自动搜索下一个的延迟 (tick)
    next-search-delay: 40
    # 音效配置
    sounds:
      # 倒计时音效 (每个数字递减时播放)
      countdown:
        sound: "CLICK"
        volume: 0.5
        pitch: 1.0
      # 完成音效 (进度条到0%变成物品时播放) - 铁砧放置音效
      complete:
        sound: "ANVIL_LAND"
        volume: 1.0
        pitch: 1.0

# 搜刮奖励物品列表
rewards:
  # 每个物品都有独立的几率配置
  items:
    diamond:
      material: DIAMOND
      amount: 1-3
      display-name: "&b钻石"
      chance: 15.0  # 15% 几率
    iron_ingot:
      material: IRON_INGOT
      amount: 2-8
      display-name: "&7铁锭"
      chance: 25.0  # 25% 几率
    gold_ingot:
      material: GOLD_INGOT
      amount: 1-5
      display-name: "&6金锭"
      chance: 20.0  # 20% 几率
    emerald:
      material: EMERALD
      amount: 1-2
      display-name: "&a绿宝石"
      chance: 12.0  # 12% 几率
    bread:
      material: BREAD
      amount: 3-10
      display-name: "&e面包"
      chance: 30.0  # 30% 几率
    cooked_beef:
      material: COOKED_BEEF
      amount: 2-6
      display-name: "&c熟牛肉"
      chance: 25.0  # 25% 几率
    diamond_sword:
      material: DIAMOND_SWORD
      amount: 1
      display-name: "&b钻石剑"
      enchantments:
        - "DAMAGE_ALL:3"
      chance: 8.0  # 8% 几率
    diamond_pickaxe:
      material: DIAMOND_PICKAXE
      amount: 1
      display-name: "&b钻石镐"
      enchantments:
        - "DIG_SPEED:3"
        - "DURABILITY:3"
      chance: 8.0  # 8% 几率
    diamond_helmet:
      material: DIAMOND_HELMET
      amount: 1
      display-name: "&b钻石头盔"
      enchantments:
        - "PROTECTION_ENVIRONMENTAL:2"
      chance: 6.0  # 6% 几率
    legendary_sword:
      material: DIAMOND_SWORD
      amount: 1
      display-name: "&5&l传说之剑"
      enchantments:
        - "DAMAGE_ALL:5"
        - "FIRE_ASPECT:2"
        - "DURABILITY:3"
      lore:
        - "&7一把传说中的神剑"
        - "&7拥有强大的力量"
      chance: 2.0  # 2% 几率
    dragon_chestplate:
      material: DIAMOND_CHESTPLATE
      amount: 1
      display-name: "&5&l龙鳞胸甲"
      enchantments:
        - "PROTECTION_ENVIRONMENTAL:4"
        - "DURABILITY:3"
      lore:
        - "&7由龙鳞制成的胸甲"
        - "&7提供极强的防护"
      chance: 1.5  # 1.5% 几率
    # 空奖励 (什么都不给)
    empty:
      material: AIR
      amount: 0
      display-name: "&7空"
      chance: 15.0  # 15% 几率什么都不给

# 消息配置
messages:
  no-permission: "&c你没有权限使用这个命令!"
  player-not-found: "&c找不到玩家: {player}"
  scavenge-given: "&a已给予 {player} 一个搜刮方块!"
  scavenge-received: "&a你收到了一个搜刮方块! 右键放置后可以搜刮!"
  config-reloaded: "&a配置文件已重新加载!"
  block-placed: "&a搜刮箱已放置! 右键点击开始搜刮!"
  scavenge-complete: "&a搜刮完成! 你获得了一些奖励!"
  inventory-full: "&c你的背包已满! 请清理后再试!"
  exploration-started: "&e开始探索新的物品..."
  exploration-complete-found: "&a探索完成！发现了: {item} x{amount}"
  exploration-complete-empty: "&7探索完成！这次没有发现任何物品。"
  all-items-claimed: "&a所有物品都已搜刮完成！等待重置时间后可再次搜刮。"
  force-reset-message: "&c搜刮箱已重置，GUI已关闭。"
  quest-completed: "&a恭喜! 你完成了任务: {quest} &e请使用 /scavenge quest 打开任务菜单领取奖励!"
  quest-reward-claimed: "&a你已成功领取任务 {quest} 的奖励!"

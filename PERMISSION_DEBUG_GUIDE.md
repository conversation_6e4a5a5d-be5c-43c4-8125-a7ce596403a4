# 权限控制调试指南

## 🔍 **问题诊断**

你遇到的问题是：虽然GUI标题显示"管理员搜刮奖励"，但只有5个物品而不是配置的20个。

## 🧪 **调试步骤**

### 1. 检查配置文件
确认 `config.yml` 中的权限配置是否正确：

```yaml
gui:
  random-items: 5  # 默认数量
  permission-items:
    "scavenge.admin":
      random-items: 20
      display-name: "&4&l管理员搜刮奖励"
```

### 2. 检查权限设置
确认你的玩家确实有管理员权限：

```bash
# 检查权限
/lp user YourPlayerName permission check scavenge.admin

# 如果没有，设置权限
/lp user YourPlayerName permission set scavenge.admin true
```

### 3. 重启服务器
应用所有修复后，重启服务器以确保所有更改生效。

### 4. 查看控制台日志
打开搜刮箱时，控制台应该显示以下调试信息：

```
[INFO] === 检查玩家权限 ===
[INFO] 玩家: YourPlayerName
[INFO] 检查权限 scavenge.admin: true
[INFO] 配置路径 gui.permission-items.scavenge.admin.random-items 的值: 20
[INFO] 使用权限 scavenge.admin 的物品数量: 20
[INFO] === 初始化搜刮箱 ===
[INFO] 玩家: YourPlayerName
[INFO] 随机物品数量: 20
[INFO] 可用槽位数量: 20
```

### 5. 可能的问题和解决方案

#### 问题1: 配置文件格式错误
**症状**: 控制台显示"配置路径的值: -1"
**解决**: 检查YAML格式，确保缩进正确

#### 问题2: 权限未正确设置
**症状**: 控制台显示"检查权限 scavenge.admin: false"
**解决**: 重新设置权限

#### 问题3: 搜刮箱使用旧数据
**症状**: GUI标题正确但物品数量错误
**解决**: 删除旧的搜刮箱并重新放置

#### 问题4: 配置未重新加载
**症状**: 修改配置后没有效果
**解决**: 使用 `/scavenge reload` 或重启服务器

## 🔧 **手动测试步骤**

### 步骤1: 清理测试环境
```bash
# 移除现有搜刮箱
/scavenge remove

# 重新加载配置
/scavenge reload
```

### 步骤2: 确认权限
```bash
# 检查权限
/lp user YourPlayerName permission check scavenge.admin

# 如果需要，设置权限
/lp user YourPlayerName permission set scavenge.admin true
```

### 步骤3: 放置新搜刮箱
```bash
# 放置新的搜刮箱
/scavenge place
```

### 步骤4: 测试不同权限等级
```bash
# 测试VIP权限 (应该显示8个物品)
/lp user YourPlayerName permission unset scavenge.admin
/lp user YourPlayerName permission set scavenge.vip true

# 测试SVIP权限 (应该显示12个物品)
/lp user YourPlayerName permission unset scavenge.vip
/lp user YourPlayerName permission set scavenge.svip true

# 测试MVP权限 (应该显示15个物品)
/lp user YourPlayerName permission unset scavenge.svip
/lp user YourPlayerName permission set scavenge.mvp true

# 测试管理员权限 (应该显示20个物品)
/lp user YourPlayerName permission unset scavenge.mvp
/lp user YourPlayerName permission set scavenge.admin true
```

## 🎯 **预期结果**

### 普通玩家 (无特殊权限)
- GUI标题: `搜刮奖励`
- 物品数量: 5个
- 控制台日志: "没有特殊权限，使用默认物品数量: 5"

### VIP玩家 (`scavenge.vip`)
- GUI标题: `VIP搜刮奖励`
- 物品数量: 8个
- 控制台日志: "使用权限 scavenge.vip 的物品数量: 8"

### SVIP玩家 (`scavenge.svip`)
- GUI标题: `SVIP搜刮奖励`
- 物品数量: 12个
- 控制台日志: "使用权限 scavenge.svip 的物品数量: 12"

### MVP玩家 (`scavenge.mvp`)
- GUI标题: `MVP搜刮奖励`
- 物品数量: 15个
- 控制台日志: "使用权限 scavenge.mvp 的物品数量: 15"

### 管理员 (`scavenge.admin`)
- GUI标题: `管理员搜刮奖励`
- 物品数量: 20个
- 控制台日志: "使用权限 scavenge.admin 的物品数量: 20"

## 🚨 **常见错误**

### 1. YAML格式错误
```yaml
# ❌ 错误格式
permission-items:
"scavenge.admin":
random-items: 20

# ✅ 正确格式
permission-items:
  "scavenge.admin":
    random-items: 20
    display-name: "&4&l管理员搜刮奖励"
```

### 2. 权限节点错误
```bash
# ❌ 错误的权限节点
scavenge.administrator

# ✅ 正确的权限节点
scavenge.admin
```

### 3. 配置路径错误
```yaml
# ❌ 错误路径
gui:
  permissions:
    admin:
      items: 20

# ✅ 正确路径
gui:
  permission-items:
    "scavenge.admin":
      random-items: 20
```

## 🔄 **故障排除流程**

1. **检查配置文件** - 确认YAML格式和路径正确
2. **检查权限设置** - 确认玩家有正确的权限
3. **重启服务器** - 确保所有更改生效
4. **查看控制台日志** - 检查调试信息
5. **删除旧搜刮箱** - 避免使用缓存的旧数据
6. **重新放置搜刮箱** - 使用新的权限配置
7. **测试不同权限** - 验证所有权限等级

## 📝 **报告问题**

如果问题仍然存在，请提供以下信息：

1. **配置文件内容** - 完整的 `config.yml` 权限部分
2. **权限检查结果** - `/lp user YourPlayerName permission check scavenge.admin`
3. **控制台日志** - 打开搜刮箱时的完整日志
4. **GUI截图** - 显示标题和物品数量
5. **服务器版本** - Minecraft和插件版本信息

## 🎯 **快速修复**

如果你想快速测试，可以尝试以下步骤：

1. **备份配置文件**
2. **重新创建配置文件**，只包含权限部分：
```yaml
gui:
  title: "&6&l搜刮奖励"
  size: 27
  random-items: 5
  permission-items:
    "scavenge.admin":
      random-items: 20
      display-name: "&4&l管理员搜刮奖励"
```
3. **重启服务器**
4. **设置权限**: `/lp user YourPlayerName permission set scavenge.admin true`
5. **放置新搜刮箱**: `/scavenge place`
6. **测试结果**

这样可以排除配置文件中其他部分可能造成的干扰。

# 数据持久化指南

## 📊 数据保存机制总览

插件使用YAML文件来保存所有数据，确保服务器重启后数据不会丢失。

## 🗂️ 数据文件结构

### 文件位置
所有数据文件都保存在 `plugins/ScavengePlugin/` 目录下：

```
plugins/ScavengePlugin/
├── config.yml          # 主配置文件
├── levels.yml           # 等级系统配置
├── quests.yml           # 任务配置
├── player_stats.yml     # 排行榜数据
├── quest_data.yml       # 任务进度数据
└── chest_data.yml       # 搜刮箱数据
```

## 📈 排行榜数据保存

### 文件：`player_stats.yml`
**保存内容**：
- 玩家统计数据
- 搜刮次数
- 完成箱子数
- 稀有物品发现数
- 任务完成数
- 游戏时间
- 物品发现统计

**数据结构**：
```yaml
players:
  "玩家UUID":
    name: "玩家名称"
    total-scavenges: 150
    chests-completed: 45
    rare-items-found: 12
    quests-completed: 8
    total-play-time: 3600000
    last-active: 1703123456789
    items-found:
      diamond: 5
      emerald: 3
      gold_ingot: 10
```

**保存时机**：
- 每次数据更新后立即保存
- 每分钟定时保存
- 服务器关闭时保存

**相关代码**：
- `LeaderboardManager.savePlayerStats()`
- `LeaderboardManager.loadPlayerStats()`

## 🎯 任务数据保存

### 文件：`quest_data.yml`
**保存内容**：
- 玩家任务进度
- 任务完成状态
- 奖励领取状态
- 完成时间

**数据结构**：
```yaml
player_progress:
  "玩家UUID":
    "任务ID":
      progress: 5
      completed: true
      claimed: false
      completed-time: 1703123456789
```

**保存时机**：
- 任务进度更新后立即保存
- 每分钟定时保存
- 服务器关闭时保存

**相关代码**：
- `QuestManager.savePlayerProgress()`
- `QuestManager.loadPlayerProgress()`

### 文件：`quests.yml`
**保存内容**：
- 任务配置（不会自动修改）
- 任务类型、目标、奖励等

## 🏆 等级数据保存

### 文件：`levels.yml`
**保存内容**：
- 等级配置
- 等级名称
- 等级要求
- 等级奖励

**特点**：
- 配置文件，不会自动修改
- 等级数据通过排行榜数据计算得出
- 支持热重载

**相关代码**：
- `LevelManager.loadLevels()`
- `LevelManager.reload()`

## 📦 搜刮箱数据保存

### 文件：`chest_data.yml`
**保存内容**：
- 搜刮箱位置
- 重置时间
- 活跃状态
- 当前使用玩家

**数据结构**：
```yaml
chests:
  "world_x_y_z":
    lastResetTime: 1703123456789
    isActive: false
    activePlayer: "玩家UUID"
```

**保存时机**：
- 搜刮箱状态改变时
- 定时保存
- 服务器关闭时保存

## 🔄 自动保存机制

### 1. 立即保存
每次重要数据更新后立即保存：
```java
// 排行榜数据
public void recordScavenge(UUID playerId, String playerName) {
    // 更新数据
    stats.addScavenge();
    // 立即保存
    savePlayerStats();
}

// 任务数据
public void updateQuestProgress(...) {
    // 更新进度
    progress.addProgress(amount);
    // 立即保存
    savePlayerProgress();
}
```

### 2. 定时保存
每分钟自动保存一次：
```java
// 排行榜管理器
Bukkit.getScheduler().runTaskTimer(plugin, () -> {
    updateOnlinePlayersTime();
    savePlayerStats(); // 定时保存
}, 20L * 60L, 20L * 60L);

// 任务管理器
Bukkit.getScheduler().runTaskTimer(plugin, () -> {
    checkAndResetExpiredQuests();
    savePlayerProgress(); // 定时保存
}, 20L * 60L, 20L * 60L);
```

### 3. 关闭时保存
服务器关闭时强制保存所有数据：
```java
@Override
public void onDisable() {
    // 保存任务数据
    if (questManager != null) {
        questManager.shutdown();
    }
    
    // 保存排行榜数据
    if (leaderboardManager != null) {
        leaderboardManager.shutdown();
    }
    
    // 保存搜刮箱数据
    if (scavengeChestManager != null) {
        scavengeChestManager.shutdown();
    }
}
```

## 🔧 数据加载机制

### 启动时自动加载
插件启动时自动加载所有数据：

```java
@Override
public void onEnable() {
    // 初始化管理器（会自动加载数据）
    questManager = new QuestManager(this);
    leaderboardManager = new LeaderboardManager(this);
    scavengeChestManager = new ScavengeChestManager(this);
}
```

### 各管理器的加载过程
1. **LeaderboardManager**：
   - 创建 `player_stats.yml`
   - 加载所有玩家统计数据
   - 启动定时保存任务

2. **QuestManager**：
   - 创建 `quest_data.yml` 和 `quests.yml`
   - 加载任务配置和玩家进度
   - 启动任务检查任务

3. **LevelManager**：
   - 创建 `levels.yml`
   - 加载等级配置
   - 设置等级奖励

## 🛡️ 数据安全保障

### 1. 异常处理
所有保存操作都有异常处理：
```java
try {
    statsConfig.save(statsFile);
} catch (IOException e) {
    plugin.getLogger().warning("保存统计数据时出错: " + e.getMessage());
}
```

### 2. 数据验证
加载数据时进行验证：
```java
try {
    UUID playerId = UUID.fromString(playerIdStr);
    // 处理数据
} catch (Exception e) {
    plugin.getLogger().warning("加载玩家统计数据时出错: " + e.getMessage());
}
```

### 3. 备份机制
建议定期备份数据文件：
- 手动备份：复制整个 `plugins/ScavengePlugin/` 目录
- 自动备份：使用服务器备份插件

## 📋 数据迁移和恢复

### 恢复数据
如果数据丢失，可以：
1. 停止服务器
2. 将备份的数据文件复制到 `plugins/ScavengePlugin/`
3. 重启服务器

### 清除数据
如果需要重置所有数据：
1. 停止服务器
2. 删除以下文件：
   - `player_stats.yml`
   - `quest_data.yml`
   - `chest_data.yml`
3. 重启服务器（会创建新的空文件）

### 迁移到新服务器
1. 复制整个 `plugins/ScavengePlugin/` 目录
2. 确保插件版本一致
3. 重启服务器

## 🔍 数据检查命令

### 调试命令
使用以下命令检查数据状态：
```
/scavenge debug stats    # 查看玩家统计数据
/scavenge debug progress # 查看任务进度
/scavenge debug quests   # 查看任务状态
```

### 重载命令
重新加载配置（不会丢失数据）：
```
/scavenge reload
```

## ✅ 数据持久化确认

### 重启后数据保留
以下数据在服务器重启后会保留：
- ✅ **排行榜数据** - 搜刮次数、排名等
- ✅ **等级数据** - 通过搜刮次数自动计算
- ✅ **任务进度** - 完成状态、进度等
- ✅ **搜刮箱状态** - 重置时间、冷却状态
- ✅ **物品统计** - 发现的物品记录

### 不会保留的数据
以下数据重启后会重置：
- ❌ **内存中的临时状态** - 正在进行的搜刮动画
- ❌ **GUI状态** - 打开的界面会关闭
- ❌ **全息图** - 会重新生成

## 🚀 性能优化

### 保存频率优化
- **立即保存**：重要数据变更
- **批量保存**：定时保存多个更改
- **异步保存**：避免阻塞主线程（如需要）

### 文件大小控制
- 定期清理过期数据
- 压缩历史记录
- 分离活跃和非活跃玩家数据

## 📝 总结

插件的数据持久化机制非常完善：

1. **多重保存**：立即保存 + 定时保存 + 关闭保存
2. **完整覆盖**：所有重要数据都会保存
3. **异常处理**：保证数据安全
4. **易于管理**：YAML格式，人类可读

**重启后数据完全保留，无需担心数据丢失！**

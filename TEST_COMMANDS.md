# 搜刮插件测试命令

## 🧪 功能测试指南

### 基础功能测试

#### 1. 插件加载测试
```
/plugins
# 确认ScavengePlugin已加载且状态为绿色
```

#### 2. 基础命令测试
```
/scavenge help
/scavenge reload
/scavenge version
```

#### 3. 搜刮箱放置测试
```
/scavenge place
# 右键点击箱子放置搜刮箱
# 确认全息图显示正确
```

### 任务系统测试

#### 1. 打开任务界面
```
/scavenge quest
# 检查GUI是否正确显示
# 确认任务列表加载正常
```

#### 2. 任务进度测试
```
# 进行几次搜刮操作
# 打开任务界面检查进度更新
# 完成任务后检查奖励领取
```

#### 3. 任务重置测试
```
# 修改系统时间或等待重置时间
# 检查任务是否正确重置
```

### 排行榜系统测试

#### 1. 打开排行榜
```
/scavenge leaderboard
/scavenge lb
/scavenge top
# 检查所有别名是否工作
```

#### 2. 排行榜切换测试
```
# 在排行榜GUI中点击不同类型按钮
# 确认数据正确切换
# 检查玩家头颅显示
```

#### 3. 统计数据测试
```
# 进行多次搜刮
# 发现稀有物品
# 完成搜刮箱
# 检查排行榜数据更新
```

### 管理员功能测试

#### 1. 权限测试
```
# 使用无权限玩家测试
# 确认只能使用基础命令
```

#### 2. 搜刮箱管理
```
/scavenge place
# 放置搜刮箱
# 破坏搜刮箱（需要权限）
# 检查全息图是否正确移除
```

#### 3. 配置重载
```
/scavenge reload
# 修改配置文件后重载
# 检查新配置是否生效
```

### 数据持久化测试

#### 1. 服务器重启测试
```
# 记录当前任务进度和排行榜数据
# 重启服务器
# 检查数据是否正确恢复
```

#### 2. 数据文件检查
```
# 检查 plugins/ScavengePlugin/quest_data.yml
# 检查 plugins/ScavengePlugin/player_stats.yml
# 确认数据格式正确
```

### GUI界面测试

#### 1. 任务GUI测试
```
# 检查任务分类显示
# 测试进度条显示
# 验证奖励领取功能
# 测试界面关闭
```

#### 2. 排行榜GUI测试
```
# 检查装饰元素
# 验证玩家头颅显示
# 测试类型切换按钮
# 检查个人排名显示
```

#### 3. 搜刮GUI测试
```
# 测试搜刮动画
# 检查进度条更新
# 验证奖励显示
# 测试自动进行功能
```

### 错误处理测试

#### 1. 无效命令测试
```
/scavenge invalid
# 应显示帮助信息
```

#### 2. 权限不足测试
```
# 无权限玩家执行管理命令
# 应显示权限不足消息
```

#### 3. 网络错误测试
```
# 断网情况下测试Minotar皮肤加载
# 应使用默认皮肤
```

### 性能测试

#### 1. 多玩家同时使用
```
# 多个玩家同时打开GUI
# 检查服务器性能
# 验证数据同步
```

#### 2. 大量数据测试
```
# 模拟大量搜刮数据
# 检查排行榜加载速度
# 验证内存使用情况
```

### 兼容性测试

#### 1. Minecraft 1.8.8测试
```
# 在1.8.8服务器上测试所有功能
# 检查材质兼容性
# 验证API调用
```

#### 2. 其他插件兼容性
```
# 与其他GUI插件共存测试
# 权限插件兼容性测试
# 经济插件集成测试
```

## 🐛 常见问题排查

### 1. 插件无法加载
- 检查Java版本兼容性
- 确认Bukkit/Spigot版本
- 查看控制台错误信息

### 2. GUI无法打开
- 检查玩家权限
- 确认配置文件格式
- 查看插件是否正确加载

### 3. 数据不保存
- 检查文件权限
- 确认数据目录存在
- 查看保存时机设置

### 4. 头颅不显示
- 检查网络连接
- 确认Minotar服务可用
- 验证玩家名称有效性

### 5. 任务不重置
- 检查系统时间
- 确认重置逻辑
- 查看配置文件设置

## 📊 测试数据示例

### 模拟测试数据
```yaml
# 可以手动修改数据文件进行测试
test_player:
  total-scavenges: 100
  chests-completed: 50
  rare-items-found: 10
  quests-completed: 25
  level: 4
  level-name: "专业探险家"
```

### 预期结果
- 排行榜正确显示
- 等级计算准确
- 综合评分合理
- GUI界面美观

通过以上测试，可以确保搜刮插件的任务系统和排行榜功能正常工作！

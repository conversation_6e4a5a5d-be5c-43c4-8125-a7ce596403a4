package com.scavenge;

import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 搜刮箱管理器 - 管理所有搜刮箱的状态和数据持久化
 */
public class ScavengeChestManager {

    private final ScavengePlugin plugin;
    private final Map<String, ScavengeChest> scavengeChests;
    private final File dataFile;
    private FileConfiguration dataConfig;

    public ScavengeChestManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.scavengeChests = new ConcurrentHashMap<>();
        this.dataFile = new File(plugin.getDataFolder(), "chests.yml");

        loadData();
        startResetTask();
    }

    /**
     * 加载搜刮箱数据
     */
    private void loadData() {
        if (!dataFile.exists()) {
            try {
                dataFile.getParentFile().mkdirs();
                dataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().warning("无法创建数据文件: " + e.getMessage());
                return;
            }
        }

        dataConfig = YamlConfiguration.loadConfiguration(dataFile);

        // 加载已保存的搜刮箱数据
        ConfigurationSection chestsSection = dataConfig.getConfigurationSection("chests");
        if (chestsSection != null) {
            for (String locationKey : chestsSection.getKeys(false)) {
                try {
                    ConfigurationSection chestSection = chestsSection.getConfigurationSection(locationKey);
                    if (chestSection != null) {
                        // 解析位置
                        String[] parts = locationKey.split(":");
                        if (parts.length == 4) {
                            String worldName = parts[0];
                            int x = Integer.parseInt(parts[1]);
                            int y = Integer.parseInt(parts[2]);
                            int z = Integer.parseInt(parts[3]);

                            Location location = new Location(
                                    plugin.getServer().getWorld(worldName), x, y, z);

                            if (location.getWorld() != null) {
                                ScavengeChest chest = new ScavengeChest(location);

                                // 加载重置时间
                                long lastResetTime = chestSection.getLong("lastResetTime", System.currentTimeMillis());
                                // 这里可以添加更多数据加载逻辑

                                scavengeChests.put(locationKey, chest);
                            }
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("加载搜刮箱数据时出错: " + locationKey + " - " + e.getMessage());
                }
            }
        }
    }

    /**
     * 保存搜刮箱数据
     */
    public void saveData() {
        try {
            // 清空现有数据
            dataConfig.set("chests", null);

            // 保存所有搜刮箱数据
            for (Map.Entry<String, ScavengeChest> entry : scavengeChests.entrySet()) {
                String locationKey = entry.getKey();
                ScavengeChest chest = entry.getValue();

                String path = "chests." + locationKey;
                dataConfig.set(path + ".lastResetTime", chest.getLastResetTime());
                dataConfig.set(path + ".isActive", chest.isActive());

                if (chest.getActivePlayer() != null) {
                    dataConfig.set(path + ".activePlayer", chest.getActivePlayer().toString());
                }
            }

            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().warning("保存搜刮箱数据时出错: " + e.getMessage());
        }
    }

    /**
     * 创建或获取搜刮箱
     */
    public ScavengeChest getOrCreateChest(Location location) {
        String locationKey = ScavengeChest.getLocationKey(location);

        ScavengeChest chest = scavengeChests.get(locationKey);
        if (chest == null) {
            chest = new ScavengeChest(location);
            scavengeChests.put(locationKey, chest);
            saveData();
        }

        return chest;
    }

    /**
     * 移除搜刮箱
     */
    public void removeChest(Location location) {
        String locationKey = ScavengeChest.getLocationKey(location);
        scavengeChests.remove(locationKey);
        saveData();
    }

    /**
     * 检查位置是否是搜刮箱
     */
    public boolean isScavengeChest(Location location) {
        String locationKey = ScavengeChest.getLocationKey(location);
        return scavengeChests.containsKey(locationKey);
    }

    /**
     * 获取搜刮箱
     */
    public ScavengeChest getChest(Location location) {
        String locationKey = ScavengeChest.getLocationKey(location);
        return scavengeChests.get(locationKey);
    }

    /**
     * 启动重置任务
     */
    private void startResetTask() {
        plugin.getServer().getScheduler().runTaskTimer(plugin, () -> {
            // 检查所有搜刮箱是否需要强制重置
            for (ScavengeChest chest : scavengeChests.values()) {
                if (chest.isReadyForReset() && chest.canReset()) {
                    // 强制重置，即使玩家正在使用
                    if (chest.isActive()) {
                        // 如果有玩家正在使用，关闭他们的GUI
                        UUID activePlayer = chest.getActivePlayer();
                        if (activePlayer != null) {
                            Player player = plugin.getServer().getPlayer(activePlayer);
                            if (player != null && player.isOnline()) {
                                player.closeInventory();
                                player.sendMessage(plugin.getMessage("force-reset-message"));
                            }
                        }
                    }

                    chest.reset();
                    plugin.getLogger().info("搜刮箱已强制重置: " + chest.getLocationKey());
                }
            }

            // 定期保存数据
            saveData();
        }, 20L * 60L, 20L * 60L); // 每分钟检查一次
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        saveData();
    }

    /**
     * 获取所有搜刮箱
     */
    public Map<String, ScavengeChest> getAllChests() {
        return scavengeChests;
    }

    /**
     * 检查所有搜刮箱的重置时间（供全局重置任务调用）
     */
    public void checkAllChestsForReset() {
        int resetCount = 0;

        for (ScavengeChest chest : scavengeChests.values()) {
            if (chest.canReset()) {
                // 强制重置搜刮箱（包括关闭GUI）
                chest.reset();
                resetCount++;
                plugin.getLogger().info("搜刮箱自动重置: " + chest.getLocationKey());
            }
        }

        if (resetCount > 0) {
            plugin.getLogger().info("全局重置检查完成，重置了 " + resetCount + " 个搜刮箱");
        }
    }

    /**
     * 重新加载配置
     */
    public void reloadConfig() {
        // 重新加载配置后，现有的搜刮箱会在下次访问时使用新的配置
        // 这里可以添加其他需要重新加载的配置相关逻辑
        plugin.getLogger().info("搜刮箱管理器配置已重新加载");
    }
}

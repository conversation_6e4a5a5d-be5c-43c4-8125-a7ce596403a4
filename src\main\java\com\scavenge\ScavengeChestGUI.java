package com.scavenge;

import com.scavenge.quest.ScavengeQuest;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 搜刮箱GUI - 处理搜刮箱的界面和搜索动画
 */
public class ScavengeChestGUI implements Listener {

    private final ScavengePlugin plugin;
    private final Player player;
    private final ScavengeChest scavengeChest;
    private final Inventory inventory;
    private final Map<Integer, BukkitTask> searchingTasks;
    private static final Map<UUID, ScavengeChestGUI> activeGUIs = new ConcurrentHashMap<>();

    public ScavengeChestGUI(ScavengePlugin plugin, Player player, ScavengeChest scavengeChest) {
        this.plugin = plugin;
        this.player = player;
        this.scavengeChest = scavengeChest;
        this.searchingTasks = new HashMap<>();

        String title = getGUITitleForPlayer(player);
        int size = plugin.getConfig().getInt("gui.size", 27);

        this.inventory = Bukkit.createInventory(null, size, title);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        setupGUI();
    }

    /**
     * 设置GUI界面
     */
    private void setupGUI() {
        // 检查是否需要根据玩家权限重新初始化或智能适配
        if (scavengeChest.needsPlayerInitialization() || isChestEmpty()) {
            // 完全重置（首次初始化或空箱子）
            scavengeChest.resetWithPlayer(player);
        } else {
            // 智能权限适配（保留已搜索的物品，调整总数量）
            scavengeChest.adaptToPlayerPermission(player);
        }

        // 实时检查是否超额完成，如果是则显示重置倒计时并关闭GUI
        if (scavengeChest.checkOverCompletion(player)) {
            player.sendMessage(plugin.getMessage("chest-over-completed")
                    .replace("{time}", String.valueOf(scavengeChest.getRemainingCooldownSeconds())));

            // 创建重置倒计时全息图
            createResetHologram();

            // 关闭GUI，不允许继续搜索
            player.closeInventory();
            return;
        }

        // 填充背景
        fillBackground();

        // 加载搜刮箱的当前状态
        loadChestState();

        // 设置搜刮箱为活跃状态
        scavengeChest.setActive(true);
        scavengeChest.setActivePlayer(player.getUniqueId());

        // 如果有正在搜索的物品，重置搜索状态并重新开始
        if (!scavengeChest.getSearchingSlots().isEmpty()) {
            // 将正在搜索的物品重新放回未搜索列表
            scavengeChest.getUnSearchedSlots().addAll(scavengeChest.getSearchingSlots());
            scavengeChest.getSearchingSlots().clear();
        }

        // 如果有未搜索的物品，开始搜索
        if (!scavengeChest.getUnSearchedSlots().isEmpty()) {
            startNextSearch();
        }
    }

    /**
     * 检查搜刮箱是否完全为空（需要初始化）
     */
    private boolean isChestEmpty() {
        return scavengeChest.getUnSearchedSlots().isEmpty() &&
                scavengeChest.getSearchingSlots().isEmpty() &&
                scavengeChest.getCompletedSlots().isEmpty();
    }

    /**
     * 填充背景 - 不填充，保持空白
     */
    private void fillBackground() {
        // 不填充背景，只在有搜刮物品的位置显示内容
    }

    /**
     * 加载搜刮箱状态
     */
    private void loadChestState() {
        // 重置所有搜索中的状态（进度条重置）
        scavengeChest.resetSearchingStates();

        // 只加载可显示的物品（排除已领取的）
        Map<Integer, ItemStack> displayableItems = scavengeChest.getDisplayableItems();
        for (Map.Entry<Integer, ItemStack> entry : displayableItems.entrySet()) {
            inventory.setItem(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 开始搜索下一个物品
     */
    private void startNextSearch() {
        Integer slot = scavengeChest.startNextSearch();
        if (slot != null) {
            // 发送开始探索消息
            player.sendMessage(plugin.getMessage("exploration-started"));
            startSearchAnimation(slot);
        } else {
            // 没有更多物品可搜索，检查是否完成
            if (scavengeChest.isCompleted()) {
                // 标记搜刮完成时间
                scavengeChest.markCompleted();
                // 搜刮完成，创建全息图
                createResetHologram();
            }
        }
    }

    /**
     * 开始搜索动画 - 支持倒计时(100%->0%)和正计时(0%->100%)两种模式
     */
    private void startSearchAnimation(int slot) {
        // 从配置文件读取进度条方向设置
        String progressMode = plugin.getConfig().getString("gui.animation.progress-direction.mode", "countdown");

        // 使用预先计算的搜索速度（基于奖励稀有度）
        final int updateInterval = scavengeChest.getSearchSpeedForSlot(slot);

        // 根据模式设置初始值和目标值
        final boolean isCountdown = "countdown".equalsIgnoreCase(progressMode);

        BukkitTask task = new BukkitRunnable() {
            int currentProgress = isCountdown ? 100 : 0; // 倒计时从100开始，正计时从0开始
            boolean taskActive = true; // 任务活跃标志

            @Override
            public void run() {
                // 检查任务是否应该继续运行
                if (!taskActive || !scavengeChest.isActive() ||
                        !scavengeChest.getActivePlayer().equals(player.getUniqueId()) ||
                        !searchingTasks.containsKey(slot)) {
                    this.cancel();
                    searchingTasks.remove(slot);
                    return;
                }

                // 更新进度并显示当前百分比
                scavengeChest.updateSearchProgress(slot, currentProgress);
                ItemStack searchingItem = plugin.getScavengeManager().createSearchingItem(currentProgress);
                inventory.setItem(slot, searchingItem);

                // 播放进度音效（每次变化都播放，与进度条完全同步）
                if ((isCountdown && currentProgress > 0) || (!isCountdown && currentProgress < 100)) {
                    playCountdownSound();
                }

                // 检查是否到达终点
                boolean isComplete = isCountdown ? (currentProgress <= 0) : (currentProgress >= 100);

                if (isComplete) {
                    // 标记任务为非活跃，防止重复执行
                    taskActive = false;

                    // 延迟一点再完成搜索，让玩家看到最终进度
                    // 完成音效将在物品显示时播放，确保音效与视觉同步
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            completeSearch(slot);
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后完成

                    this.cancel();
                    return;
                }

                // 根据模式更新进度 - 严格按1递增/递减
                if (isCountdown) {
                    // 倒计时模式：100 -> 99 -> 98 -> ... -> 1 -> 0
                    currentProgress = Math.max(0, currentProgress - 1);
                } else {
                    // 正计时模式：0 -> 1 -> 2 -> ... -> 99 -> 100
                    currentProgress = Math.min(100, currentProgress + 1);
                }
            }
        }.runTaskTimer(plugin, 0L, updateInterval);

        searchingTasks.put(slot, task);
    }

    /**
     * 播放倒计时音效
     */
    private void playCountdownSound() {
        String soundName = plugin.getConfig().getString("gui.animation.sounds.countdown.sound", "CLICK");
        float volume = (float) plugin.getConfig().getDouble("gui.animation.sounds.countdown.volume", 0.5);
        float pitch = (float) plugin.getConfig().getDouble("gui.animation.sounds.countdown.pitch", 1.0);

        try {
            Sound sound = Sound.valueOf(soundName);
            player.playSound(player.getLocation(), sound, volume, pitch);
        } catch (IllegalArgumentException e) {
            // 如果音效名称无效，使用默认音效
            player.playSound(player.getLocation(), Sound.CLICK, volume, pitch);
        }
    }

    /**
     * 播放完成音效
     */
    private void playCompleteSound() {
        String soundName = plugin.getConfig().getString("gui.animation.sounds.complete.sound", "ANVIL_LAND");
        float volume = (float) plugin.getConfig().getDouble("gui.animation.sounds.complete.volume", 1.0);
        float pitch = (float) plugin.getConfig().getDouble("gui.animation.sounds.complete.pitch", 1.0);

        try {
            Sound sound = Sound.valueOf(soundName);
            player.playSound(player.getLocation(), sound, volume, pitch);
        } catch (IllegalArgumentException e) {
            // 如果音效名称无效，使用默认铁砧放置音效
            player.playSound(player.getLocation(), Sound.ANVIL_LAND, volume, pitch);
        }
    }

    /**
     * 完成搜索 - 自动根据几率变成物品，然后继续下一个
     */
    private void completeSearch(int slot) {
        // 调试信息

        ScavengeReward reward = plugin.getScavengeManager().selectRandomReward();
        ItemStack rewardItem = null;

        if (reward != null) {
            rewardItem = reward.createItemStack();
            if (rewardItem != null) {
            } else {
            }
        } else {
        }

        // 保存奖励信息到搜刮箱数据中（持久化）
        scavengeChest.setRewardForSlot(slot, reward);
        scavengeChest.completeSearch(slot, rewardItem);
        searchingTasks.remove(slot);

        // 记录搜刮统计和更新任务进度
        plugin.getLeaderboardManager().recordScavenge(player.getUniqueId(), player.getName());
        plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
                ScavengeQuest.QuestGoal.SCAVENGE_COUNT, 1);

        // 检查是否是稀有物品
        if (reward != null && reward.isRare()) {
            plugin.getLeaderboardManager().recordRareItemFound(player.getUniqueId(),
                    player.getName(), reward.getDisplayName());
            plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
                    ScavengeQuest.QuestGoal.FIND_RARE_ITEMS, 1);
        }

        // 处理奖励
        if (reward != null) {
            if (reward.getType() == ScavengeReward.RewardType.COMMAND) {
                // 指令奖励 - 执行指令并显示图标
                reward.giveReward(player);

                // 显示指令奖励的图标
                ItemStack displayItem = reward.createDisplayItem();
                inventory.setItem(slot, displayItem);

                // 播放完成音效 - 与物品显示同步
                playCompleteSound();

                String message = plugin.getMessage("exploration-complete-found")
                        .replace("{item}", reward.getDisplayName())
                        .replace("{amount}", "1");
                player.sendMessage(message);
            } else {
                // 物品奖励
                rewardItem = reward.createItemStack();
                if (rewardItem != null && rewardItem.getType() != Material.AIR) {
                    inventory.setItem(slot, rewardItem);

                    // 播放完成音效 - 与物品显示同步
                    playCompleteSound();

                    String itemName = rewardItem.hasItemMeta() && rewardItem.getItemMeta().hasDisplayName()
                            ? rewardItem.getItemMeta().getDisplayName()
                            : rewardItem.getType().toString();
                    String message = plugin.getMessage("exploration-complete-found")
                            .replace("{item}", itemName)
                            .replace("{amount}", String.valueOf(rewardItem.getAmount()));
                    player.sendMessage(message);
                } else {
                    // 如果是空奖励，显示空气方块或移除物品
                    inventory.setItem(slot, new ItemStack(Material.AIR));

                    // 播放完成音效 - 即使是空奖励也播放
                    playCompleteSound();

                    player.sendMessage(plugin.getMessage("exploration-complete-empty"));
                }
            }
        } else {
            // 如果没有奖励，显示空气方块
            inventory.setItem(slot, new ItemStack(Material.AIR));

            // 播放完成音效 - 即使没有奖励也播放
            playCompleteSound();

            player.sendMessage(plugin.getMessage("exploration-complete-empty"));
        }

        // 延迟后自动开始搜索下一个
        int nextSearchDelay = plugin.getConfig().getInt("gui.animation.next-search-delay", 40);
        new BukkitRunnable() {
            @Override
            public void run() {
                if (scavengeChest.isActive() && scavengeChest.getActivePlayer().equals(player.getUniqueId())) {
                    startNextSearch();
                } else if (scavengeChest.isCompleted()) {
                    // 如果搜刮已完成，创建全息图
                    createResetHologram();
                }
            }
        }.runTaskLater(plugin, nextSearchDelay);
    }

    /**
     * 打开GUI
     */
    public void open() {
        activeGUIs.put(player.getUniqueId(), this);
        player.openInventory(inventory);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player))
            return;

        Player clicker = (Player) event.getWhoClicked();
        if (!clicker.equals(player))
            return;

        if (!event.getInventory().equals(inventory))
            return;

        event.setCancelled(true);

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR)
            return;

        // 检查是否点击了背景玻璃
        if (clickedItem.getType() == Material.STAINED_GLASS_PANE) {
            return;
        }

        int slot = event.getSlot();

        // 检查是否是正在搜索的物品 - 如果是，直接返回，不处理点击
        if (searchingTasks.containsKey(slot)) {
            // 正在搜索中，忽略点击，避免干扰进度条
            return;
        }

        // 检查是否是已完成的奖励物品
        if (scavengeChest.getCompletedSlots().contains(slot) && !scavengeChest.getClaimedSlots().contains(slot)) {
            // 获取对应的奖励
            ScavengeReward reward = scavengeChest.getRewardForSlot(slot);

            if (reward != null && reward.getType() == ScavengeReward.RewardType.COMMAND) {
                // 指令奖励的纸质记录可以被领取，但不给予物品，只是标记为已领取
                player.sendMessage("§a已确认指令执行记录！");
                inventory.setItem(slot, new ItemStack(Material.AIR));
                scavengeChest.claimItem(slot); // 标记为已领取
            } else {
                // 普通物品奖励
                // 检查玩家背包是否有空间
                if (player.getInventory().firstEmpty() == -1) {
                    player.sendMessage(plugin.getMessage("inventory-full"));
                    return;
                }

                // 给予物品并标记为已领取
                ItemStack claimedItem = clickedItem.clone();
                player.getInventory().addItem(claimedItem);
                inventory.setItem(slot, new ItemStack(Material.AIR));
                scavengeChest.claimItem(slot); // 标记为已领取

                // 更新物品收集任务进度（只有从搜刮箱获得的物品才计入）
                updateItemCollectionProgress(player, claimedItem);
            }

            // 检查是否所有物品都已被领取
            if (scavengeChest.isAllItemsClaimed()) {
                // 记录搜刮箱完成
                plugin.getLeaderboardManager().recordChestCompleted(player.getUniqueId(), player.getName());
                plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
                        ScavengeQuest.QuestGoal.COMPLETE_CHESTS, 1);

                player.sendMessage(plugin.getMessage("all-items-claimed"));

                // 创建倒计时全息图
                createResetHologram();
            }
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player))
            return;

        Player closer = (Player) event.getPlayer();
        if (!closer.equals(player))
            return;

        if (!event.getInventory().equals(inventory))
            return;

        // 设置搜刮箱为非活跃状态
        scavengeChest.setActive(false);
        scavengeChest.setActivePlayer(null);

        // 取消所有搜索任务
        for (BukkitTask task : searchingTasks.values()) {
            task.cancel();
        }
        searchingTasks.clear();

        // 创建或更新全息图
        createOrUpdateHologram();

        // 移除GUI引用
        activeGUIs.remove(player.getUniqueId());

        // 注销事件监听器
        InventoryClickEvent.getHandlerList().unregister(this);
        InventoryCloseEvent.getHandlerList().unregister(this);
    }

    /**
     * 创建或更新全息图
     */
    private void createOrUpdateHologram() {
        if (!plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            return;
        }

        // 检查是否所有物品都已完成并领取
        if (scavengeChest.isCompleted() && scavengeChest.isAllItemsClaimed()) {
            // 所有物品都已完成并领取，显示倒计时
            createResetHologram();
        } else {
            // 还在进行中，显示进度信息
            plugin.getHologramManager().createProgressHologram(scavengeChest.getLocation(), player);
        }
    }

    /**
     * 创建重置倒计时全息图
     */
    private void createResetHologram() {
        if (plugin.getConfig().getBoolean("scavenge-chest.hologram.enabled", true)) {
            // 使用搜刮完成时间 + 重置时间作为重置时间点
            long resetTime = scavengeChest.getLastResetTime() +
                    (plugin.getConfig().getInt("scavenge-chest.reset-time", 300) * 1000L);
            plugin.getHologramManager().createCountdownHologram(scavengeChest.getLocation(), resetTime);
        }
    }

    public static ScavengeChestGUI getActiveGUI(Player player) {
        return activeGUIs.get(player.getUniqueId());
    }

    /**
     * 更新物品收集任务进度（只有从搜刮箱获得的物品才计入）
     */
    private void updateItemCollectionProgress(Player player, ItemStack item) {
        if (item == null || item.getType() == Material.AIR)
            return;

        String material = item.getType().name();
        String displayName = null;

        // 获取物品显示名称
        ItemMeta meta = item.getItemMeta();
        if (meta != null && meta.hasDisplayName()) {
            displayName = meta.getDisplayName();
        }

        // 更新物品收集任务进度
        plugin.getQuestManager().updateItemCollectionProgress(
                player.getUniqueId(),
                material,
                displayName,
                item.getAmount());
    }

    /**
     * 根据玩家权限获取GUI标题
     */
    private String getGUITitleForPlayer(Player player) {
        // 检查权限配置，从高级权限到低级权限
        String[] permissions = {
                "scavenge.admin",
                "scavenge.mvp",
                "scavenge.svip",
                "scavenge.vip"
        };

        for (String permission : permissions) {
            if (player.hasPermission(permission)) {
                String path = "gui.permission-items." + permission + ".display-name";
                String title = plugin.getConfig().getString(path, null);
                if (title != null) {
                    return title.replace("&", "§");
                }
            }
        }

        // 如果没有特殊权限，使用默认标题
        return plugin.getConfig().getString("gui.title", "&6搜刮奖励")
                .replace("&", "§");
    }
}

# 任务配置指南

## 📁 配置文件结构

现在任务系统使用单独的配置文件，让配置更加整洁：

```
plugins/ScavengePlugin/
├── config.yml          # 主配置文件（搜刮系统设置）
├── quests.yml          # 任务配置文件（所有任务设置）
├── quest_data.yml      # 任务数据文件（玩家进度，自动生成）
└── player_stats.yml    # 排行榜数据文件（玩家统计，自动生成）
```

## 🎯 任务配置文件 (quests.yml)

### 基础结构
```yaml
quests:
  任务ID:
    name: "任务名称"
    description: "任务描述"
    type: "任务类型"
    goal: "任务目标"
    target-amount: 目标数量
    rewards:
      - "奖励指令1"
      - "奖励指令2"
    console: true/false
    rare: true/false
```

### 任务类型 (type)
- `DAILY` - 每日任务（24小时重置）
- `WEEKLY` - 每周任务（7天重置）
- `SPECIAL` - 特殊任务（30天重置）

### 任务目标 (goal)
- `SCAVENGE_COUNT` - 搜刮次数
- `FIND_RARE_ITEMS` - 发现稀有物品
- `COMPLETE_CHESTS` - 完成搜刮箱
- `COLLECT_COMMAND_ITEM` - 收集指令给予的物品

## 🎮 任务示例

### 1. 基础搜刮任务
```yaml
daily_scavenge_5:
  name: "每日搜刮者"
  description: "完成5次搜刮操作"
  type: "DAILY"
  goal: "SCAVENGE_COUNT"
  target-amount: 5
  rewards:
    - "give {player} diamond 3"
    - "give {player} emerald 5"
  console: true
  rare: false
```

### 2. 稀有物品收集任务
```yaml
daily_rare_items:
  name: "稀有物品猎人"
  description: "发现2个稀有物品"
  type: "DAILY"
  goal: "FIND_RARE_ITEMS"
  target-amount: 2
  rewards:
    - "give {player} emerald 8"
    - "give {player} experience_bottle 3"
  console: true
  rare: false
```

### 3. 指令物品收集任务
```yaml
daily_collect_diamonds:
  name: "钻石收集者"
  description: "收集3个钻石"
  type: "DAILY"
  goal: "COLLECT_COMMAND_ITEM"
  target-amount: 3
  target-material: "DIAMOND"
  target-display-name: null  # 任意钻石都可以
  rewards:
    - "give {player} emerald_block 1"
    - "give {player} experience_bottle 5"
  console: true
  rare: false
```

### 4. 特定名称物品收集任务
```yaml
weekly_collect_special_sword:
  name: "传说武器收集者"
  description: "收集1把传说之剑"
  type: "WEEKLY"
  goal: "COLLECT_COMMAND_ITEM"
  target-amount: 1
  target-material: "DIAMOND_SWORD"
  target-display-name: "传说之剑"  # 必须包含此显示名称
  rewards:
    - "give {player} diamond_block 5"
    - "give {player} experience_bottle 30"
  console: true
  rare: true
```

## ⚙️ 配置参数详解

### 必填参数
| 参数 | 类型 | 说明 |
|------|------|------|
| `name` | String | 任务显示名称 |
| `description` | String | 任务描述 |
| `type` | String | 任务类型 |
| `goal` | String | 任务目标类型 |
| `target-amount` | Integer | 目标完成数量 |
| `rewards` | List | 奖励指令列表 |
| `console` | Boolean | 是否以控制台执行指令 |
| `rare` | Boolean | 是否为稀有奖励 |

### 可选参数（仅COLLECT_COMMAND_ITEM任务）
| 参数 | 类型 | 说明 |
|------|------|------|
| `target-material` | String | 目标物品材质 |
| `target-display-name` | String | 目标物品显示名称 |

## 🔄 任务重置设置

在quests.yml底部配置重置时间：

```yaml
# 任务重置时间设置
reset-times:
  daily: "00:00"      # 每日0点重置
  weekly: "MONDAY"    # 每周一重置
  special: 30         # 特殊任务30天重置

# 任务完成音效设置
sounds:
  quest-progress: "EXPERIENCE_ORB_PICKUP"  # 任务进度更新音效
  quest-complete: "LEVEL_UP"               # 任务完成音效
  reward-claim: "ITEM_PICKUP"              # 奖励领取音效
```

## 🎵 音效配置

支持的1.8.8音效：
- `EXPERIENCE_ORB_PICKUP` - 经验球拾取音效
- `LEVEL_UP` - 升级音效
- `ITEM_PICKUP` - 物品拾取音效
- `CLICK` - 点击音效
- `SUCCESSFUL_HIT` - 成功击中音效

## 📝 奖励指令格式

### 变量替换
- `{player}` - 玩家名称
- `{amount}` - 数量（如果适用）

### 指令示例
```yaml
rewards:
  - "give {player} diamond 5"                    # 给予钻石
  - "give {player} emerald_block 2"              # 给予绿宝石块
  - "experience give {player} 100"               # 给予经验
  - "money give {player} 1000"                   # 给予金钱（需要经济插件）
  - "lp user {player} permission set some.perm"  # 给予权限（需要LuckPerms）
  - "broadcast {player} 完成了困难任务！"          # 广播消息
```

## 🔧 管理命令

### 重载配置
```
/scavenge reload
```
这会重新加载所有配置文件，包括quests.yml。

### 查看任务
```
/scavenge quest
```
玩家可以查看当前可用的任务。

## 📊 数据文件说明

### quest_data.yml
存储玩家任务进度，格式：
```yaml
player_progress:
  玩家UUID:
    任务ID:
      progress: 当前进度
      completed: 是否完成
      claimed: 是否已领取
      completed-time: 完成时间
```

### player_stats.yml
存储玩家统计数据，用于排行榜系统。

## ⚠️ 注意事项

1. **任务ID唯一性**：每个任务ID必须唯一
2. **材质名称**：使用正确的Minecraft 1.8.8材质名称
3. **指令权限**：确保控制台有执行奖励指令的权限
4. **文件编码**：使用UTF-8编码保存配置文件
5. **YAML格式**：注意缩进和语法正确性

## 🚀 快速开始

1. 编辑 `quests.yml` 文件
2. 添加你想要的任务
3. 执行 `/scavenge reload` 重载配置
4. 玩家使用 `/scavenge quest` 查看任务

这样的配置结构让任务管理更加清晰和方便！

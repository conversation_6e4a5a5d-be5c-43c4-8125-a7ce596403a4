# 搜刮插件 - 任务系统和排行榜功能

## 🎯 新增功能概览

### 1. 搜刮任务系统
- **每日任务**：24小时重置的短期目标
- **每周任务**：7天重置的长期挑战  
- **特殊任务**：30天重置的特殊活动
- **自动进度追踪**：实时更新任务进度
- **奖励系统**：支持物品和指令奖励

### 2. 排行榜系统
- **多维度排名**：总搜刮次数、完成箱子、稀有物品、任务完成、综合评分
- **前十排名显示**：精美的GUI界面展示
- **玩家头颅**：集成Minotar皮肤系统
- **等级系统**：10个等级，从新手到搜刮之神
- **实时统计**：自动记录玩家数据

## 🎮 任务系统详解

### 任务类型
```yaml
# 每日任务示例
daily_scavenge_5:
  name: "每日搜刮者"
  description: "完成5次搜刮"
  type: "DAILY"
  goal: "SCAVENGE_COUNT"
  target-amount: 5
  rewards:
    - "give {player} diamond 3"
    - "give {player} emerald 5"
  console: true
  rare: false
```

### 任务目标类型
- `SCAVENGE_COUNT` - 搜刮次数
- `FIND_RARE_ITEMS` - 发现稀有物品
- `COMPLETE_CHESTS` - 完成搜刮箱
- `COLLECT_SPECIFIC` - 收集特定物品

### 稀有物品判定
- **物品奖励**：概率 ≤ 10% 自动标记为稀有
- **指令奖励**：通过配置文件 `rare: true/false` 手动指定

## 🏆 排行榜系统详解

### 排行榜类型
1. **总搜刮次数** - 展示最活跃的搜刮者
2. **完成搜刮箱** - 展示最有耐心的探索者
3. **稀有物品发现** - 展示最幸运的玩家
4. **任务完成** - 展示最勤奋的任务完成者
5. **综合评分** - 综合所有数据的总排名

### 等级系统
| 等级 | 名称 | 所需搜刮次数 |
|------|------|-------------|
| 1 | 新手探索者 | 0-9 |
| 2 | 初级搜刮者 | 10-49 |
| 3 | 熟练搜刮者 | 50-99 |
| 4 | 专业探险家 | 100-249 |
| 5 | 资深搜刮者 | 250-499 |
| 6 | 搜刮专家 | 500-999 |
| 7 | 搜刮大师 | 1000-1999 |
| 8 | 传奇探索者 | 2000-4999 |
| 9 | 搜刮宗师 | 5000-9999 |
| 10 | 搜刮之神 | 10000+ |

### 综合评分算法
```
评分 = 搜刮次数 × 2 + 完成箱子 × 3 + 稀有物品 × 5 + 任务完成 × 10
```

## 🎨 GUI界面特色

### 任务GUI
- **进度条显示**：直观的任务完成进度
- **剩余时间**：显示任务到期时间
- **一键领取**：点击完成任务即可领取奖励
- **任务分类**：按类型分组显示

### 排行榜GUI
- **装饰边框**：金色玻璃和奖杯装饰
- **玩家头颅**：使用Minotar API显示真实皮肤
- **排名标识**：前三名特殊颜色和图标
- **详细信息**：等级、效率、发现率等统计

## 🔧 新增命令

### 玩家命令
- `/scavenge quest` - 打开任务界面
- `/scavenge leaderboard` - 打开排行榜
- `/scavenge lb` - 排行榜简写
- `/scavenge top` - 排行榜别名

### 管理员命令
所有原有命令保持不变，新增：
- Tab补全支持新命令

## 📁 文件结构

### 新增Java类
```
src/main/java/com/scavenge/
├── quest/
│   ├── ScavengeQuest.java          # 任务数据类
│   ├── PlayerQuestProgress.java    # 玩家任务进度
│   └── QuestManager.java           # 任务管理器
├── leaderboard/
│   ├── PlayerStats.java            # 玩家统计数据
│   └── LeaderboardManager.java     # 排行榜管理器
├── gui/
│   ├── QuestGUI.java               # 任务GUI界面
│   └── LeaderboardGUI.java         # 排行榜GUI界面
└── utils/
    └── SkullUtils.java             # 头颅工具类
```

### 数据文件
- `quest_data.yml` - 任务进度数据
- `player_stats.yml` - 玩家统计数据

## 🌐 Minotar集成

### 皮肤URL格式
- 标准头像：`https://minotar.net/avatar/{玩家名}/64.png`
- 不同尺寸：`https://minotar.net/avatar/{玩家名}/{尺寸}.png`

### 特性
- **自动缓存**：避免重复请求
- **错误处理**：无效玩家名使用默认皮肤
- **1.8.8兼容**：完美支持旧版本

## 🔄 自动化功能

### 数据同步
- **定时保存**：每分钟自动保存数据
- **任务重置**：到期任务自动重置
- **在线时间**：自动记录玩家游戏时间

### 事件监听
- **搜刮完成**：自动更新统计和任务进度
- **稀有物品**：自动识别并记录
- **任务完成**：实时通知玩家

## 🎯 使用建议

### 服务器管理员
1. 根据服务器规模调整任务难度
2. 定期查看排行榜数据平衡游戏
3. 利用稀有物品系统增加游戏趣味性

### 玩家体验
1. 每日登录查看任务进度
2. 通过排行榜了解自己的位置
3. 努力完成任务获得丰厚奖励

## 🔧 配置说明

### 任务配置
每个任务支持以下配置项：
- `name` - 任务名称
- `description` - 任务描述  
- `type` - 任务类型（DAILY/WEEKLY/SPECIAL）
- `goal` - 任务目标类型
- `target-amount` - 目标数量
- `rewards` - 奖励列表
- `console` - 是否以控制台执行指令
- `rare` - 是否为稀有奖励（仅指令奖励）

### 消息配置
新增消息配置项：
- `quest-completed` - 任务完成消息
- `quest-reward-claimed` - 奖励领取消息

这些新功能大大增强了搜刮插件的可玩性和竞争性，为玩家提供了更多的游戏目标和成就感！

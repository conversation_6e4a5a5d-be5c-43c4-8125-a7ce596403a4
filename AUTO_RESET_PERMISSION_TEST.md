# 自动重置权限检测测试指南

## 🔍 **重置场景分析**

搜刮箱有三种重置场景，都需要正确处理权限检测：

### **场景1：手动重置**
- **触发**: `/scavenge reset all` 或 `/scavenge reset target`
- **调用**: `chest.reset()`
- **状态**: ✅ 已修复 - 会重置 `hasBeenInitializedWithPlayer = false`

### **场景2：玩家打开时自动重置**
- **触发**: 玩家右键搜刮箱，且搜刮箱可以重置
- **调用**: `chest.reset()` (在 ScavengeListener.java 第95行)
- **状态**: ✅ 已修复 - 会重置 `hasBeenInitializedWithPlayer = false`

### **场景3：定时任务自动重置**
- **触发**: 定时任务检查，搜刮箱超时自动重置
- **调用**: `chest.reset()` (在 ScavengeChestManager.java 第176行)
- **状态**: ✅ 已修复 - 会重置 `hasBeenInitializedWithPlayer = false`

## 🧪 **测试场景**

### **测试1：定时自动重置**

#### **准备工作**
1. 设置较短的重置时间（方便测试）：
   ```yaml
   scavenge-chest:
     reset-time: 60  # 60秒重置
   ```

2. 给测试玩家VIP权限：
   ```bash
   /lp user <玩家名> permission set scavenge.vip true
   ```

3. 确认VIP配置：
   ```yaml
   gui:
     permission-items:
       scavenge.vip:
         random-items: 10
   ```

#### **测试步骤**
1. **完成搜刮箱**: 玩家搜刮完所有物品并领取
2. **等待重置**: 等待60秒，让定时任务自动重置搜刮箱
3. **检查日志**: 控制台应显示 `"搜刮箱已强制重置: <位置>"`
4. **玩家重新打开**: VIP玩家右键点击搜刮箱
5. **验证结果**: 应显示10个物品（VIP数量），不是5个（默认数量）

#### **预期日志**
```
[INFO] [ScavengePlugin] 搜刮箱已强制重置: world:100:64:200
[INFO] [ScavengePlugin] === 检查玩家权限 ===
[INFO] [ScavengePlugin] 玩家: VIPPlayer
[INFO] [ScavengePlugin] 检查权限 scavenge.vip: true
[INFO] [ScavengePlugin] 使用权限 scavenge.vip 的物品数量: 10
```

### **测试2：玩家打开时自动重置**

#### **测试步骤**
1. **完成搜刮箱**: 玩家搜刮完所有物品并领取
2. **等待冷却**: 等待重置时间结束（搜刮箱可以重置）
3. **玩家打开**: VIP玩家右键点击搜刮箱
4. **验证消息**: 玩家应收到 "搜刮箱已重置，可以重新搜刮了!" 消息
5. **验证物品数量**: 应显示10个物品（VIP数量）

#### **预期流程**
```
玩家右键 → 检查 isReadyForReset() && canReset() → 调用 reset() → 
重置权限标志 → 打开GUI → 检测权限 → 显示VIP数量
```

### **测试3：不同权限玩家测试**

#### **多权限测试**
1. **设置多个测试玩家**:
   - 玩家A: `scavenge.admin` (20个物品)
   - 玩家B: `scavenge.mvp` (15个物品)  
   - 玩家C: `scavenge.vip` (10个物品)
   - 玩家D: 无权限 (5个物品)

2. **完成搜刮箱**: 任意玩家完成搜刮箱

3. **等待自动重置**: 让定时任务自动重置搜刮箱

4. **分别测试**: 每个玩家依次打开同一个搜刮箱

5. **验证结果**: 每个玩家看到的物品数量应与其权限对应

### **测试4：重置时机测试**

#### **强制重置测试**
1. **玩家正在搜刮**: 玩家A正在搜刮箱子（GUI打开）
2. **时间到达**: 重置时间到达，触发强制重置
3. **GUI关闭**: 玩家A的GUI应该自动关闭
4. **收到消息**: 玩家A收到强制重置消息
5. **重新打开**: 玩家A重新打开搜刮箱
6. **验证权限**: 应显示玩家A权限对应的物品数量

## 📊 **配置文件设置**

### **测试用配置**
```yaml
# 缩短重置时间便于测试
scavenge-chest:
  reset-time: 60  # 60秒重置（正式环境建议300秒）
  
# 权限物品数量配置
gui:
  random-items: 5  # 默认数量
  permission-items:
    scavenge.admin:
      random-items: 20
      display-name: "&c&l管理员搜刮箱 &7(20个物品)"
    scavenge.mvp:
      random-items: 15
      display-name: "&6&lMVP搜刮箱 &7(15个物品)"
    scavenge.svip:
      random-items: 12
      display-name: "&d&lSVIP搜刮箱 &7(12个物品)"
    scavenge.vip:
      random-items: 10
      display-name: "&a&lVIP搜刮箱 &7(10个物品)"
```

### **消息配置**
```yaml
messages:
  force-reset-message: "&e搜刮箱已自动重置，请重新打开！"
  scavenge-block:
    available-message: "&a搜刮箱已重置，可以重新搜刮了!"
```

## 🔧 **调试命令**

### **权限管理**
```bash
# 给予权限
/lp user <玩家名> permission set scavenge.vip true
/lp user <玩家名> permission set scavenge.mvp true

# 移除权限
/lp user <玩家名> permission unset scavenge.vip
/lp user <玩家名> permission unset scavenge.mvp

# 查看权限
/lp user <玩家名> permission info
```

### **搜刮箱管理**
```bash
# 手动重置所有搜刮箱
/scavenge reset all

# 重置目标搜刮箱
/scavenge reset target

# 查看搜刮箱列表
/scavenge list

# 重新加载配置
/scavenge reload
```

### **调试信息**
```bash
# 查看玩家任务进度
/scavenge debug progress

# 查看玩家统计
/scavenge debug stats

# 查看任务信息
/scavenge debug quests
```

## ✅ **验证清单**

### **自动重置后权限检测**
- [ ] 定时重置后，VIP玩家看到10个物品
- [ ] 定时重置后，MVP玩家看到15个物品
- [ ] 定时重置后，管理员看到20个物品
- [ ] 定时重置后，普通玩家看到5个物品

### **玩家打开时重置**
- [ ] 冷却结束后，玩家打开时自动重置
- [ ] 重置消息正确显示
- [ ] 权限检测正确工作

### **强制重置处理**
- [ ] 玩家正在使用时，GUI正确关闭
- [ ] 强制重置消息正确发送
- [ ] 重新打开时权限检测正确

### **日志验证**
- [ ] 控制台显示权限检测日志
- [ ] 控制台显示重置成功日志
- [ ] 没有错误或警告信息

## 🎯 **预期结果**

所有三种重置场景都应该：
1. **正确重置权限标志**: `hasBeenInitializedWithPlayer = false`
2. **触发权限重新检测**: 下次打开时调用 `resetWithPlayer(player)`
3. **显示正确物品数量**: 根据玩家权限显示对应数量的物品
4. **保持功能一致性**: 无论哪种重置方式，结果都一致

现在所有重置场景都应该正确处理权限检测了！

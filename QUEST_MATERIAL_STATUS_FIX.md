# 任务材质状态区分修复

## ✅ 已完成的修复

### 🎯 **问题描述**
从图片可以看到：
- 任务完成后显示"可领取"，但材质还是绿宝石
- 需要区分"可领取"和"已领取"两种状态的材质
- 需要在任务完成时发送聊天消息提醒玩家去任务菜单领取
- 不要自动领取奖励

### 🔧 **修复内容**

#### 1. 修复任务材质状态区分 ✅

**修复前**:
```java
if (completed) {
    material = Material.EMERALD;  // 完成后都是绿宝石
    status = progress.isClaimed() ? "§a已领取" : "§e可领取";
    displayName = "§a已完成 §6" + quest.getName();
}
```

**修复后**:
```java
if (completed) {
    if (progress.isClaimed()) {
        material = Material.EMERALD;  // 已领取 = 绿宝石
        status = "§a已领取";
        displayName = "§a已完成 §6" + quest.getName();
    } else {
        material = Material.DIAMOND;  // 可领取 = 钻石
        status = "§e可领取";
        displayName = "§e可领取 §6" + quest.getName();
    }
} else {
    material = getQuestMaterial(quest.getType());  // 进行中 = 原始材质
    status = "§7进行中";
    displayName = "§6" + quest.getName();
}
```

#### 2. 修复点击事件处理 ✅

**新增钻石材质点击处理**:
```java
// 处理任务点击（钻石表示可领取，绿宝石表示已领取）
if (clickedItem.getType() == Material.DIAMOND) {
    handleQuestClaim(clicker, event.getSlot());
}
// 处理其他任务点击（包括绿宝石等）
else if (clickedItem.getType() == Material.WATCH || clickedItem.getType() == Material.BOOK ||
         clickedItem.getType() == Material.NETHER_STAR || clickedItem.getType() == Material.PAPER ||
         clickedItem.getType() == Material.EMERALD) {
    handleQuestClick(clicker, event.getSlot());
}
```

#### 3. 修复任务完成消息 ✅

**修复前**:
```yaml
quest-completed: "&a恭喜! 你完成了任务: {quest}"
```

**修复后**:
```yaml
quest-completed: "&a恭喜! 你完成了任务: {quest} &e请使用 /scavenge quest 打开任务菜单领取奖励!"
```

## 🎯 **修复效果**

### 任务状态材质区分

#### 🕐 **进行中任务**
- **材质**: 时钟/书/下界之星/纸 (根据任务类型)
- **名称**: "§6任务名"
- **状态**: "§7进行中"
- **点击**: 无效果

#### 💎 **可领取任务**
- **材质**: 钻石 (Material.DIAMOND)
- **名称**: "§e可领取 §6任务名"
- **状态**: "§e可领取"
- **点击**: 领取奖励

#### 🟢 **已领取任务**
- **材质**: 绿宝石 (Material.EMERALD)
- **名称**: "§a已完成 §6任务名"
- **状态**: "§a已领取"
- **点击**: 无效果

### 任务完成流程

#### 1. 任务进度达到目标
```
进度: 5/5 (100%)
状态: 进行中 → 可领取
材质: 时钟 → 钻石
名称: "每日搜刮者" → "可领取 每日搜刮者"
```

#### 2. 发送聊天消息
```
§a恭喜! 你完成了任务: 每日搜刮者 §e请使用 /scavenge quest 打开任务菜单领取奖励!
```

#### 3. 玩家点击钻石任务
```
执行奖励命令
状态: 可领取 → 已领取
材质: 钻石 → 绿宝石
名称: "可领取 每日搜刮者" → "已完成 每日搜刮者"
```

#### 4. 发送领取确认消息
```
§a你已成功领取任务 每日搜刮者 的奖励!
```

## 📊 **预期显示效果**

### 可领取任务显示为：
```
💎 可领取 每日搜刮者 (#0347)
完成5次搜刮操作

类型: 每日任务
目标: 搜刮次数
进度: 5/5
████████████████████ 100%
状态: 可领取

剩余时间: 23小时55分钟

奖励:
- 钻石 x2
- 铁锭 x5

点击领取奖励!
```

### 已领取任务显示为：
```
🟢 已完成 每日搜刮者 (#0347)
完成5次搜刮操作

类型: 每日任务
目标: 搜刮次数
进度: 5/5
████████████████████ 100%
状态: 已领取

剩余时间: 23小时55分钟

奖励:
- 钻石 x2
- 铁锭 x5
```

### 进行中任务显示为：
```
🕐 每日搜刮者 (#0347)
完成5次搜刮操作

类型: 每日任务
目标: 搜刮次数
进度: 3/5
████████████░░░░░░░░ 60%
状态: 进行中

剩余时间: 23小时55分钟

奖励:
- 钻石 x2
- 铁锭 x5
```

## 🧪 **测试步骤**

### 1. 重启服务器
应用所有修复后重启服务器。

### 2. 完成任务测试
1. 进行搜刮操作直到任务完成
2. 检查聊天栏是否收到完成消息
3. 打开任务GUI检查材质是否为钻石
4. 检查任务名称是否显示"可领取"

### 3. 领取奖励测试
1. 点击钻石材质的任务
2. 检查是否执行奖励命令
3. 检查任务材质是否变为绿宝石
4. 检查任务名称是否变为"已完成"
5. 检查聊天栏是否收到领取确认消息

### 4. 状态持久化测试
1. 重启服务器
2. 检查已领取任务是否保持绿宝石状态
3. 检查可领取任务是否保持钻石状态

## 🔍 **调试信息**

如果任务状态显示不正确，检查：

### 1. 控制台日志
查看任务完成日志：
```
=== 任务进度更新开始 ===
当前进度: 5/5
任务完成: true
发送完成消息: 每日搜刮者
=== 任务进度更新结束 ===
```

### 2. 聊天消息
检查是否收到：
```
§a恭喜! 你完成了任务: 每日搜刮者 §e请使用 /scavenge quest 打开任务菜单领取奖励!
```

### 3. GUI显示
- 可领取任务应该是钻石材质
- 已领取任务应该是绿宝石材质
- 进行中任务应该是原始材质

### 4. 点击功能
- 钻石任务可以点击领取
- 绿宝石任务点击无效果
- 其他材质任务根据完成状态处理

## 🚀 **总结**

通过这次修复，任务系统现在能够：

1. ✅ **清晰区分任务状态** - 不同状态使用不同材质
2. ✅ **正确显示任务名称** - 根据状态显示不同前缀
3. ✅ **及时通知玩家** - 任务完成时发送聊天消息
4. ✅ **手动领取奖励** - 不自动领取，需要玩家点击
5. ✅ **状态持久化** - 重启后状态保持正确

### 材质状态映射：
- 🕐 **进行中** = 时钟/书/下界之星/纸
- 💎 **可领取** = 钻石
- 🟢 **已领取** = 绿宝石

现在任务系统的状态显示更加清晰，玩家可以一眼看出哪些任务可以领取奖励！

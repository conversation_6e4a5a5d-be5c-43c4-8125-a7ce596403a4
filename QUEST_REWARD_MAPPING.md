# 任务奖励映射说明

## 📋 概述

任务系统已经根据你的 `config.yml` 文件中的奖励配置进行了设计。所有任务奖励都基于你现有的搜刮奖励物品，确保整个插件的奖励体系保持一致。

## 🎯 基于config.yml的奖励物品

### 你的配置文件中的奖励物品：
1. **钻石** (DIAMOND) - 1-3个，20%概率
2. **铁锭** (IRON_INGOT) - 2-8个，25%概率  
3. **金锭** (GOLD_INGOT) - 1-5个，20%概率
4. **绿宝石** (EMERALD) - 1-2个，15%概率
5. **面包** (BREAD) - 3-10个，18.5%概率
6. **经验瓶** (EXPERIENCE_BOTTLE) - 5-10个，10%概率
7. **金币奖励** (指令) - 100金币，8%概率
8. **随机传送** (指令) - 5%概率
9. **管理员奖励** (指令) - 5个钻石，2%概率

## 📊 任务奖励设计原则

### 1. 奖励递增原则
- **每日任务**: 使用基础数量的奖励物品
- **每周任务**: 使用中等数量的奖励物品 + 金币奖励
- **特殊任务**: 使用大量奖励物品 + 高额金币 + 广播

### 2. 物品数量对应
- 基于config.yml中的数量范围设计
- 考虑任务难度调整数量
- 保持奖励的吸引力

## 🎮 具体任务奖励映射

### 每日任务 (简单，基础奖励)

#### daily_scavenge_5 - 每日搜刮者
- **目标**: 完成5次搜刮
- **奖励**: 
  - 钻石 x2 (基于config.yml中的钻石)
  - 铁锭 x5 (基于config.yml中的铁锭)

#### daily_complete_2_chests - 每日探索者  
- **目标**: 完成2个搜刮箱
- **奖励**:
  - 金锭 x3 (基于config.yml中的金锭)
  - 面包 x5 (基于config.yml中的面包)

#### daily_rare_items - 稀有物品猎人
- **目标**: 发现1个稀有物品
- **奖励**:
  - 绿宝石 x1 (基于config.yml中的绿宝石)
  - 经验瓶 x3 (基于config.yml中的经验瓶)

#### daily_collect_diamonds - 钻石收集者
- **目标**: 收集2个钻石
- **奖励**:
  - 金锭 x4 (交叉奖励)
  - 经验瓶 x2

#### daily_collect_iron - 铁锭收集者
- **目标**: 收集8个铁锭
- **奖励**:
  - 钻石 x1 (交叉奖励)
  - 面包 x8

#### daily_collect_gold - 黄金收集者
- **目标**: 收集5个金锭
- **奖励**:
  - 绿宝石 x2 (交叉奖励)
  - 经验瓶 x4

### 每周任务 (中等难度，增加金币奖励)

#### weekly_scavenge_30 - 每周搜刮大师
- **目标**: 完成30次搜刮
- **奖励**:
  - 钻石 x10 (基于config.yml)
  - 绿宝石 x5 (基于config.yml)
  - 经验瓶 x15 (基于config.yml)
  - 金币 x500 (基于config.yml中的金币奖励)

#### weekly_complete_15_chests - 每周探索大师
- **目标**: 完成15个搜刮箱
- **奖励**:
  - 钻石 x8
  - 金锭 x20 (基于config.yml)
  - 经验瓶 x12

#### weekly_rare_items_5 - 稀有物品专家
- **目标**: 发现5个稀有物品
- **奖励**:
  - 钻石 x6
  - 绿宝石 x8 (基于config.yml)
  - 经验瓶 x20
  - 广播消息 (基于config.yml中的广播奖励)

#### weekly_collect_emeralds - 绿宝石收集者
- **目标**: 收集8个绿宝石
- **奖励**:
  - 钻石 x5
  - 经验瓶 x10
  - 金币 x300

#### weekly_collect_experience - 经验收集者
- **目标**: 收集20个经验瓶
- **奖励**:
  - 钻石 x4
  - 绿宝石 x6
  - 金锭 x15

### 特殊任务 (高难度，丰厚奖励)

#### special_scavenge_master - 搜刮宗师
- **目标**: 完成100次搜刮
- **奖励**:
  - 钻石 x64 (基于config.yml，大量)
  - 绿宝石 x32 (基于config.yml)
  - 金锭 x64 (基于config.yml)
  - 经验瓶 x50 (基于config.yml)
  - 金币 x2000 (基于config.yml中的金币奖励)
  - 广播消息 (基于config.yml中的广播奖励)

#### special_chest_master - 搜刮箱大师
- **目标**: 完成50个搜刮箱
- **奖励**:
  - 钻石 x48
  - 绿宝石 x24
  - 铁锭 x128 (基于config.yml)
  - 经验瓶 x40
  - 金币 x1500

#### special_rare_collector - 稀有物品收藏家
- **目标**: 发现20个稀有物品
- **奖励**:
  - 钻石 x32
  - 绿宝石 x48 (基于config.yml)
  - 经验瓶 x60
  - 金币 x3000
  - 广播消息

#### special_collect_bread - 面包收集大师
- **目标**: 收集100个面包
- **奖励**:
  - 钻石 x20
  - 金锭 x30
  - 经验瓶 x25
  - 金币 x1000

#### special_money_collector - 财富收集者
- **目标**: 完成25次搜刮 (模拟获得金币奖励)
- **说明**: 基于config.yml中金币奖励8%概率，大约25次搜刮能获得2次金币奖励
- **奖励**:
  - 钻石 x15
  - 绿宝石 x20
  - 金币 x5000 (大额金币奖励)
  - 广播消息

#### special_explorer - 传送探索者
- **目标**: 完成80次搜刮 (模拟获得传送奖励)
- **说明**: 基于config.yml中传送奖励5%概率，大约80次搜刮能获得4次传送奖励
- **奖励**:
  - 钻石 x25
  - 末影珍珠 x16 (基于config.yml中传送奖励的显示材料)
  - 经验瓶 x30
  - 金币 x1200

## 🔄 奖励一致性保证

### 1. 材质一致性
- 所有任务奖励都使用config.yml中定义的材质
- 保持整个插件的视觉一致性

### 2. 数量合理性
- 基于config.yml中的数量范围
- 根据任务难度适当调整
- 确保奖励有吸引力但不过度

### 3. 指令一致性
- 使用相同的金币指令格式 (`eco give {player} 数量`)
- 使用相同的广播格式 (`broadcast 消息`)
- 保持指令执行方式一致

## 🎯 稀有物品定义

基于config.yml中的概率，以下物品被认为是稀有的：
- **经验瓶** (10%概率)
- **金币奖励** (8%概率)
- **随机传送** (5%概率)
- **管理员奖励** (2%概率)

这些物品在任务系统中会触发"发现稀有物品"的进度。

## 📈 奖励价值递增

### 每日任务价值: 低
- 基础材料数量
- 无金币奖励
- 无广播

### 每周任务价值: 中
- 中等材料数量
- 包含金币奖励
- 部分包含广播

### 特殊任务价值: 高
- 大量材料
- 高额金币奖励
- 包含广播荣誉

这样的设计确保了任务系统与你现有的搜刮奖励完美融合，为玩家提供一致且有吸引力的游戏体验！

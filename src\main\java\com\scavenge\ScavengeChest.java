package com.scavenge;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.*;

/**
 * 搜刮箱数据类 - 存储搜刮箱的状态信息
 */
public class ScavengeChest {

    private final String locationKey;
    private final Location location;
    private final Map<Integer, ItemStack> items; // 槽位 -> 物品
    private final Set<Integer> unSearchedSlots; // 未搜索的槽位
    private final Set<Integer> searchingSlots; // 正在搜索的槽位
    private final Set<Integer> completedSlots; // 已完成的槽位
    private final Set<Integer> claimedSlots; // 已领取的槽位
    private final Map<Integer, ScavengeReward> slotRewards; // 存储每个槽位对应的奖励
    private final Map<Integer, Integer> slotSearchSpeeds; // 每个槽位的搜索速度（更新间隔tick）
    private long lastResetTime; // 上次重置时间
    private boolean isActive; // 是否有玩家正在使用
    private UUID activePlayer; // 当前使用的玩家
    private boolean hasBeenInitializedWithPlayer; // 是否已经根据玩家权限初始化过
    private boolean isOverCompleted; // 是否超额完成（权限降级时已探索物品超过新权限限制）

    public ScavengeChest(Location location) {
        this.location = location;
        this.locationKey = getLocationKey(location);
        this.items = new HashMap<>();
        this.unSearchedSlots = new HashSet<>();
        this.searchingSlots = new HashSet<>();
        this.completedSlots = new HashSet<>();
        this.claimedSlots = new HashSet<>();
        this.slotRewards = new HashMap<>();
        this.slotSearchSpeeds = new HashMap<>();
        this.lastResetTime = System.currentTimeMillis();
        this.isActive = false;
        this.activePlayer = null;
        this.hasBeenInitializedWithPlayer = false;

        // 使用默认物品数量初始化
        initializeItems(null);
    }

    /**
     * 初始化搜刮箱内容
     */
    private void initializeItems(Player player) {
        ScavengePlugin plugin = ScavengePlugin.getInstance();
        int randomItems = getRandomItemsForPlayer(player);

        plugin.getLogger().info("=== 初始化搜刮箱 ===");
        plugin.getLogger().info("玩家: " + (player != null ? player.getName() : "null"));
        plugin.getLogger().info("随机物品数量: " + randomItems);

        // 获取可用槽位
        int[] availableSlots = getAvailableSlots(randomItems);
        plugin.getLogger().info("可用槽位数量: " + availableSlots.length);

        // 清空之前的数据
        items.clear();
        unSearchedSlots.clear();
        searchingSlots.clear();
        completedSlots.clear();
        claimedSlots.clear();
        slotRewards.clear();
        slotSearchSpeeds.clear();

        // 预先选择奖励并计算搜索速度
        for (int i = 0; i < Math.min(randomItems, availableSlots.length); i++) {
            int slot = availableSlots[i];

            // 预先选择奖励
            ScavengeReward reward = plugin.getScavengeManager().selectRandomReward();
            slotRewards.put(slot, reward);

            // 根据奖励稀有度计算搜索速度
            int searchSpeed = calculateSearchSpeed(reward);
            slotSearchSpeeds.put(slot, searchSpeed);

            // 添加未搜索的物品显示
            ItemStack unSearchedItem = plugin.getScavengeManager().createUnSearchedItem();
            items.put(slot, unSearchedItem);
            unSearchedSlots.add(slot);
        }
    }

    /**
     * 根据奖励配置的进度条时间计算搜索速度
     */
    private int calculateSearchSpeed(ScavengeReward reward) {
        if (reward == null) {
            return 3; // 默认速度
        }

        // 直接使用奖励配置的进度条时间
        return reward.getProgressTime();
    }

    /**
     * 根据玩家权限获取随机物品数量
     */
    private int getRandomItemsForPlayer(Player player) {
        ScavengePlugin plugin = ScavengePlugin.getInstance();

        if (player == null) {
            // 如果没有玩家信息，使用默认数量
            plugin.getLogger().info("玩家为空，使用默认物品数量: 5");
            return plugin.getConfig().getInt("gui.random-items", 5);
        }

        plugin.getLogger().info("=== 检查玩家权限 ===");
        plugin.getLogger().info("玩家: " + player.getName());

        // 检查权限配置，从高级权限到低级权限
        String[] permissions = {
                "scavenge.admin",
                "scavenge.mvp",
                "scavenge.svip",
                "scavenge.vip"
        };

        for (String permission : permissions) {
            boolean hasPermission = player.hasPermission(permission);
            plugin.getLogger().info("检查权限 " + permission + ": " + hasPermission);

            if (hasPermission) {
                String path = "gui.permission-items." + permission + ".random-items";
                int items = plugin.getConfig().getInt(path, -1);
                plugin.getLogger().info("配置路径 " + path + " 的值: " + items);

                if (items > 0) {
                    plugin.getLogger().info("使用权限 " + permission + " 的物品数量: " + items);
                    return items;
                }
            }
        }

        // 如果没有特殊权限，使用默认数量
        int defaultItems = plugin.getConfig().getInt("gui.random-items", 5);
        plugin.getLogger().info("没有特殊权限，使用默认物品数量: " + defaultItems);
        return defaultItems;
    }

    /**
     * 获取可用的槽位 - 随机分布在箱子中
     */
    private int[] getAvailableSlots(int randomItems) {
        ScavengePlugin plugin = ScavengePlugin.getInstance();
        int guiSize = plugin.getConfig().getInt("gui.size", 27);

        // 排除关闭按钮位置（最后一个位置）
        List<Integer> availablePositions = new ArrayList<>();
        for (int i = 0; i < guiSize - 1; i++) {
            availablePositions.add(i);
        }

        // 随机打乱位置
        Collections.shuffle(availablePositions);

        // 返回前N个位置
        int[] result = new int[Math.min(randomItems, availablePositions.size())];
        for (int i = 0; i < result.length; i++) {
            result[i] = availablePositions.get(i);
        }

        return result;
    }

    /**
     * 检查是否可以重置
     */
    public boolean canReset() {
        ScavengePlugin plugin = ScavengePlugin.getInstance();
        int resetTimeSeconds = plugin.getConfig().getInt("scavenge-chest.reset-time", 300);
        long resetTimeMs = resetTimeSeconds * 1000L;

        return (System.currentTimeMillis() - lastResetTime) >= resetTimeMs;
    }

    /**
     * 获取剩余冷却时间（秒）
     */
    public long getRemainingCooldownSeconds() {
        ScavengePlugin plugin = ScavengePlugin.getInstance();
        int resetTimeSeconds = plugin.getConfig().getInt("scavenge-chest.reset-time", 300);
        long resetTimeMs = resetTimeSeconds * 1000L;
        long elapsed = System.currentTimeMillis() - lastResetTime;
        long remaining = resetTimeMs - elapsed;

        return Math.max(0, remaining / 1000L);
    }

    /**
     * 重置搜刮箱
     */
    public void reset() {
        this.lastResetTime = System.currentTimeMillis();
        this.isActive = false;
        this.activePlayer = null;
        this.hasBeenInitializedWithPlayer = false; // 重置玩家初始化标志，下次打开时会根据玩家权限重新初始化
        initializeItems(null);

        // 移除全息图
        ScavengePlugin plugin = ScavengePlugin.getInstance();
        if (plugin != null && plugin.getHologramManager() != null) {
            plugin.getHologramManager().removeHologram(location);
        }
    }

    /**
     * 根据玩家权限重置搜刮箱
     */
    public void resetWithPlayer(Player player) {
        this.lastResetTime = System.currentTimeMillis();
        this.isActive = false;
        this.activePlayer = null;
        this.hasBeenInitializedWithPlayer = true; // 标记已经根据玩家权限初始化
        initializeItems(player);

        // 移除全息图
        ScavengePlugin plugin = ScavengePlugin.getInstance();
        if (plugin != null && plugin.getHologramManager() != null) {
            plugin.getHologramManager().removeHologram(location);
        }
    }

    /**
     * 检查是否已完成所有搜刮
     */
    public boolean isCompleted() {
        return unSearchedSlots.isEmpty() && searchingSlots.isEmpty();
    }

    /**
     * 检查是否所有物品都已被领取（用于重置判断）
     */
    public boolean isReadyForReset() {
        return isCompleted() && isAllItemsClaimed();
    }

    /**
     * 标记搜刮完成时间（用于重置倒计时）
     */
    public void markCompleted() {
        if (isCompleted()) {
            this.lastResetTime = System.currentTimeMillis();
        }
    }

    /**
     * 开始搜索下一个物品
     */
    public Integer startNextSearch() {
        if (unSearchedSlots.isEmpty()) {
            return null;
        }

        // 随机选择一个未搜索的槽位
        Integer[] slots = unSearchedSlots.toArray(new Integer[0]);
        int randomIndex = new Random().nextInt(slots.length);
        int slot = slots[randomIndex];

        // 移动到搜索中状态
        unSearchedSlots.remove(slot);
        searchingSlots.add(slot);

        return slot;
    }

    /**
     * 完成搜索
     */
    public void completeSearch(int slot, ItemStack reward) {
        searchingSlots.remove(slot);
        completedSlots.add(slot);

        if (reward != null) {
            items.put(slot, reward);
        } else {
            items.remove(slot);
        }
    }

    /**
     * 更新搜索进度
     */
    public void updateSearchProgress(int slot, int progress) {
        if (searchingSlots.contains(slot)) {
            ScavengePlugin plugin = ScavengePlugin.getInstance();
            ItemStack searchingItem = plugin.getScavengeManager().createSearchingItem(progress);
            items.put(slot, searchingItem);
        }
    }

    /**
     * 生成位置键
     */
    public static String getLocationKey(Location location) {
        return location.getWorld().getName() + ":" +
                location.getBlockX() + ":" +
                location.getBlockY() + ":" +
                location.getBlockZ();
    }

    // Getters and Setters
    public String getLocationKey() {
        return locationKey;
    }

    public Location getLocation() {
        return location;
    }

    public Map<Integer, ItemStack> getItems() {
        return items;
    }

    public Set<Integer> getUnSearchedSlots() {
        return unSearchedSlots;
    }

    public Set<Integer> getSearchingSlots() {
        return searchingSlots;
    }

    public Set<Integer> getCompletedSlots() {
        return completedSlots;
    }

    public long getLastResetTime() {
        return lastResetTime;
    }

    public boolean isActive() {
        return isActive;
    }

    public UUID getActivePlayer() {
        return activePlayer;
    }

    public void setActive(boolean active) {
        this.isActive = active;
    }

    public void setActivePlayer(UUID activePlayer) {
        this.activePlayer = activePlayer;
    }

    public Set<Integer> getClaimedSlots() {
        return claimedSlots;
    }

    /**
     * 标记物品为已领取
     */
    public void claimItem(int slot) {
        claimedSlots.add(slot);
        items.remove(slot); // 从显示中移除
    }

    /**
     * 重置所有搜索中的状态（当玩家关闭箱子时）
     */
    public void resetSearchingStates() {
        // 将所有搜索中的槽位重新设为未搜索
        unSearchedSlots.addAll(searchingSlots);
        searchingSlots.clear();

        // 重新添加未搜索的物品显示
        ScavengePlugin plugin = ScavengePlugin.getInstance();
        for (Integer slot : unSearchedSlots) {
            if (!claimedSlots.contains(slot)) {
                ItemStack unSearchedItem = plugin.getScavengeManager().createUnSearchedItem();
                items.put(slot, unSearchedItem);
            }
        }
    }

    /**
     * 检查是否所有物品都已被领取（包括指令奖励记录）
     */
    public boolean isAllItemsClaimed() {
        // 首先检查是否所有搜索都已完成
        if (!isCompleted()) {
            return false; // 还有未搜索或正在搜索的物品，不能算完成
        }

        // 获取当前搜刮箱中实际的物品总数（已完成的槽位数量）
        int totalCompletedSlots = completedSlots.size();

        // 如果没有已完成的槽位，说明还没有开始搜刮或者搜刮箱为空
        if (totalCompletedSlots == 0) {
            return false;
        }

        // 检查是否所有已完成的物品都已被领取
        return claimedSlots.size() >= totalCompletedSlots;
    }

    /**
     * 获取当前可显示的物品（排除已领取的）
     */
    public Map<Integer, ItemStack> getDisplayableItems() {
        Map<Integer, ItemStack> displayable = new HashMap<>();
        for (Map.Entry<Integer, ItemStack> entry : items.entrySet()) {
            if (!claimedSlots.contains(entry.getKey())) {
                displayable.put(entry.getKey(), entry.getValue());
            }
        }
        return displayable;
    }

    /**
     * 存储槽位对应的奖励
     */
    public void setRewardForSlot(int slot, ScavengeReward reward) {
        slotRewards.put(slot, reward);
    }

    /**
     * 获取槽位对应的奖励
     */
    public ScavengeReward getRewardForSlot(int slot) {
        return slotRewards.get(slot);
    }

    /**
     * 获取槽位的搜索速度
     */
    public int getSearchSpeedForSlot(int slot) {
        return slotSearchSpeeds.getOrDefault(slot, 3); // 默认3tick间隔
    }

    /**
     * 检查搜刮箱是否需要根据玩家权限重新初始化
     */
    public boolean needsPlayerInitialization() {
        // 如果从未根据玩家权限初始化过，需要重新初始化
        return !hasBeenInitializedWithPlayer;
    }

    /**
     * 获取是否已经根据玩家权限初始化过
     */
    public boolean hasBeenInitializedWithPlayer() {
        return hasBeenInitializedWithPlayer;
    }

    /**
     * 检查是否超额完成（权限降级时已探索物品超过新权限限制）
     */
    public boolean isOverCompleted() {
        return isOverCompleted;
    }

    /**
     * 检查是否应该显示重置倒计时（完成或超额完成）
     */
    public boolean shouldShowResetCountdown() {
        return (isCompleted() && isAllItemsClaimed()) || isOverCompleted();
    }

    /**
     * 智能权限适配 - 根据新玩家权限调整物品数量，保留已搜索的物品
     */
    public void adaptToPlayerPermission(Player player) {
        ScavengePlugin plugin = ScavengePlugin.getInstance();

        // 获取新玩家权限对应的物品数量
        int newItemCount = plugin.getScavengeManager().getRandomItemCountForPlayer(player);

        // 计算当前已使用的槽位总数
        int currentTotalSlots = unSearchedSlots.size() + searchingSlots.size() +
                completedSlots.size() + claimedSlots.size();

        plugin.getLogger().info("=== 智能权限适配 ===");
        plugin.getLogger().info("玩家: " + player.getName());
        plugin.getLogger().info("新权限物品数量: " + newItemCount);
        plugin.getLogger().info("当前总槽位数: " + currentTotalSlots);
        plugin.getLogger().info("未搜索: " + unSearchedSlots.size());
        plugin.getLogger().info("搜索中: " + searchingSlots.size());
        plugin.getLogger().info("已完成: " + completedSlots.size());
        plugin.getLogger().info("已领取: " + claimedSlots.size());

        if (newItemCount > currentTotalSlots) {
            // 权限更高，需要增加物品
            int additionalItems = newItemCount - currentTotalSlots;
            plugin.getLogger().info("需要增加物品数量: " + additionalItems);

            // 找到可用的槽位（避免与现有物品冲突）
            Set<Integer> usedSlots = new HashSet<>();
            usedSlots.addAll(unSearchedSlots);
            usedSlots.addAll(searchingSlots);
            usedSlots.addAll(completedSlots);
            usedSlots.addAll(claimedSlots);

            // 添加新的未搜索物品
            for (int i = 0; i < 54 && additionalItems > 0; i++) {
                if (!usedSlots.contains(i)) {
                    unSearchedSlots.add(i);

                    // 创建未搜索物品的显示
                    ItemStack unSearchedItem = plugin.getScavengeManager().createUnSearchedItem();
                    items.put(i, unSearchedItem);

                    // 预设搜索速度
                    ScavengeReward tempReward = plugin.getScavengeManager().selectRandomReward();
                    if (tempReward != null) {
                        int searchSpeed = calculateSearchSpeed(tempReward);
                        slotSearchSpeeds.put(i, searchSpeed);
                    }

                    additionalItems--;
                    plugin.getLogger().info("添加新物品到槽位: " + i);
                }
            }

        } else if (newItemCount < currentTotalSlots) {
            // 权限更低，需要减少物品（只能减少未搜索的物品）
            int itemsToRemove = currentTotalSlots - newItemCount;
            plugin.getLogger().info("需要移除物品数量: " + itemsToRemove);

            // 只移除未搜索的物品，保留已搜索、已完成、已领取的物品
            Iterator<Integer> iterator = unSearchedSlots.iterator();
            while (iterator.hasNext() && itemsToRemove > 0) {
                Integer slot = iterator.next();
                iterator.remove();
                items.remove(slot);
                slotSearchSpeeds.remove(slot);
                itemsToRemove--;
                plugin.getLogger().info("移除未搜索物品槽位: " + slot);
            }

            // 检查是否已探索的物品数量超过了新权限限制
            int exploredItems = searchingSlots.size() + completedSlots.size() + claimedSlots.size();
            if (exploredItems >= newItemCount) {
                plugin.getLogger().info("已探索物品数量(" + exploredItems + ")超过新权限限制(" + newItemCount + ")，标记为超额完成");
                // 标记为超额完成状态
                this.isOverCompleted = true;
                // 设置完成时间用于重置倒计时
                if (this.lastResetTime == 0) {
                    this.lastResetTime = System.currentTimeMillis();
                }
            } else if (itemsToRemove > 0) {
                plugin.getLogger().info("无法移除更多物品，因为剩余的都是已搜索/已完成/已领取的物品");
            }
        } else {
            // 物品数量相同，无需调整
            plugin.getLogger().info("物品数量相同，无需调整");
        }

        // 标记为已根据玩家权限初始化
        this.hasBeenInitializedWithPlayer = true;

        plugin.getLogger().info("=== 适配完成 ===");
        plugin.getLogger().info("最终未搜索: " + unSearchedSlots.size());
        plugin.getLogger().info("最终搜索中: " + searchingSlots.size());
        plugin.getLogger().info("最终已完成: " + completedSlots.size());
        plugin.getLogger().info("最终已领取: " + claimedSlots.size());
        plugin.getLogger().info("最终总数: " + (unSearchedSlots.size() + searchingSlots.size() +
                completedSlots.size() + claimedSlots.size()));
    }
}

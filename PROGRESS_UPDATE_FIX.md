# 任务进度和排行榜数据不更新问题修复

## 🐛 发现的问题

### 1. 任务初始化问题
任务在创建时没有设置开始时间和结束时间，导致 `quest.isActive()` 和 `quest.isExpired()` 方法返回错误结果。

### 2. 数据保存问题
数据保存频率可能不够，或者保存时机不对。

### 3. 调试信息缺失
没有足够的调试信息来确定问题所在。

## 🔧 修复方案

### 修复1: 任务初始化时设置时间
需要在任务创建时设置正确的开始时间和结束时间。

### 修复2: 添加调试信息
在关键位置添加调试日志，帮助定位问题。

### 修复3: 强制数据保存
在每次更新后立即保存数据。

## 📝 具体修复步骤

### 步骤1: 修复ScavengeQuest构造函数
在ScavengeQuest类中，确保任务创建时设置正确的时间。

### 步骤2: 修复QuestManager的任务加载
在加载任务时设置正确的时间范围。

### 步骤3: 添加调试日志
在updateQuestProgress方法中添加详细的调试信息。

### 步骤4: 立即保存数据
在每次进度更新后立即保存数据。

## 🚀 临时解决方案

### 手动测试命令
可以添加一些测试命令来手动触发进度更新：

```java
// 在ScavengeCommand中添加测试命令
case "test-quest":
    if (sender instanceof Player) {
        Player player = (Player) sender;
        plugin.getQuestManager().updateQuestProgress(
            player.getUniqueId(), 
            ScavengeQuest.QuestGoal.SCAVENGE_COUNT, 
            1
        );
        player.sendMessage("§a手动更新了搜刮任务进度!");
    }
    return true;

case "test-stats":
    if (sender instanceof Player) {
        Player player = (Player) sender;
        plugin.getLeaderboardManager().recordScavenge(
            player.getUniqueId(), 
            player.getName()
        );
        player.sendMessage("§a手动更新了排行榜统计!");
    }
    return true;
```

### 检查数据文件
检查以下文件是否正确生成和更新：
- `plugins/ScavengePlugin/quest_data.yml`
- `plugins/ScavengePlugin/player_stats.yml`

### 重载配置
使用 `/scavenge reload` 命令重载配置，看是否能解决问题。

## 🔍 调试步骤

### 1. 检查任务是否正确加载
在服务器控制台查看是否有 "加载任务: xxx" 的消息。

### 2. 检查任务状态
添加命令查看任务状态：
```java
case "debug-quests":
    sender.sendMessage("§6=== 任务调试信息 ===");
    for (ScavengeQuest quest : plugin.getQuestManager().getQuests().values()) {
        sender.sendMessage("§e任务: " + quest.getName());
        sender.sendMessage("§7- 活跃: " + quest.isActive());
        sender.sendMessage("§7- 过期: " + quest.isExpired());
        sender.sendMessage("§7- 开始时间: " + quest.getStartTime());
        sender.sendMessage("§7- 结束时间: " + quest.getEndTime());
    }
    return true;
```

### 3. 检查玩家进度
添加命令查看玩家进度：
```java
case "debug-progress":
    if (sender instanceof Player) {
        Player player = (Player) sender;
        sender.sendMessage("§6=== 玩家进度调试信息 ===");
        Map<String, PlayerQuestProgress> progress = 
            plugin.getQuestManager().getPlayerProgress().get(player.getUniqueId());
        if (progress != null) {
            for (Map.Entry<String, PlayerQuestProgress> entry : progress.entrySet()) {
                sender.sendMessage("§e任务: " + entry.getKey());
                sender.sendMessage("§7- 进度: " + entry.getValue().getCurrentProgress());
                sender.sendMessage("§7- 完成: " + entry.getValue().isCompleted());
            }
        } else {
            sender.sendMessage("§c没有找到玩家进度数据");
        }
    }
    return true;
```

## 🎯 最可能的原因

### 1. 任务时间未设置
任务创建时没有设置开始时间和结束时间，导致 `isActive()` 返回 false。

### 2. 配置文件路径问题
quests.yml 文件可能没有正确加载。

### 3. 事件触发问题
搜刮事件可能没有正确触发任务进度更新。

## 🔧 快速修复代码

### 修复任务时间设置
```java
// 在QuestManager的loadQuests方法中，创建任务后添加：
scavengeQuest.setStartTime(System.currentTimeMillis());
switch (scavengeQuest.getType()) {
    case DAILY:
        scavengeQuest.setEndTime(System.currentTimeMillis() + (24 * 60 * 60 * 1000L));
        break;
    case WEEKLY:
        scavengeQuest.setEndTime(System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000L));
        break;
    case SPECIAL:
        scavengeQuest.setEndTime(System.currentTimeMillis() + (30 * 24 * 60 * 60 * 1000L));
        break;
}
```

### 添加调试日志
```java
// 在updateQuestProgress方法开始处添加：
plugin.getLogger().info("更新任务进度: 玩家=" + playerId + ", 目标=" + goalType + ", 数量=" + amount);
plugin.getLogger().info("当前任务数量: " + quests.size());

// 在循环中添加：
plugin.getLogger().info("检查任务: " + quest.getName() + 
    ", 目标匹配=" + (quest.getGoal() == goalType) + 
    ", 活跃=" + quest.isActive() + 
    ", 未过期=" + !quest.isExpired());
```

### 强制保存数据
```java
// 在updateQuestProgress方法结束前添加：
savePlayerProgress(); // 立即保存数据

// 在LeaderboardManager的record方法中添加：
savePlayerStats(); // 立即保存统计数据
```

## 📊 验证修复效果

### 1. 重启服务器
应用修复后重启服务器。

### 2. 测试搜刮
进行几次搜刮操作，检查：
- 控制台是否有调试日志
- 任务进度是否更新
- 排行榜数据是否更新

### 3. 检查数据文件
查看数据文件是否有新的数据写入。

### 4. 使用调试命令
使用添加的调试命令检查任务状态和玩家进度。

这些修复应该能解决任务进度和排行榜数据不更新的问题！

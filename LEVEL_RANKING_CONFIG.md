x# 等级排名配置指南

## 📊 等级系统设置位置

### 🔧 配置文件位置
等级排名系统的配置在以下文件中：

```
src/main/java/com/scavenge/leaderboard/PlayerStats.java
```

## 🎯 当前等级设置

### 等级列表
当前系统有10个等级，基于搜刮次数：

```java
public String getLevelName() {
    if (totalScavenges >= 1000) return "搜刮之神";
    if (totalScavenges >= 500) return "搜刮宗师";
    if (totalScavenges >= 300) return "搜刮专家";
    if (totalScavenges >= 200) return "搜刮达人";
    if (totalScavenges >= 100) return "搜刮高手";
    if (totalScavenges >= 50) return "熟练搜刮者";
    if (totalScavenges >= 25) return "活跃搜刮者";
    if (totalScavenges >= 10) return "进阶搜刮者";
    if (totalScavenges >= 5) return "初级搜刮者";
    return "新手探索者";
}

public int getLevel() {
    if (totalScavenges >= 1000) return 10;
    if (totalScavenges >= 500) return 9;
    if (totalScavenges >= 300) return 8;
    if (totalScavenges >= 200) return 7;
    if (totalScavenges >= 100) return 6;
    if (totalScavenges >= 50) return 5;
    if (totalScavenges >= 25) return 4;
    if (totalScavenges >= 10) return 3;
    if (totalScavenges >= 5) return 2;
    return 1;
}
```

## 🔧 如何修改等级设置

### 1. 修改等级名称
在 `PlayerStats.java` 文件中找到 `getLevelName()` 方法，修改等级名称：

```java
public String getLevelName() {
    if (totalScavenges >= 1000) return "传说大师";      // 修改这里
    if (totalScavenges >= 500) return "钻石搜刮者";     // 修改这里
    if (totalScavenges >= 300) return "黄金搜刮者";     // 修改这里
    // ... 继续修改其他等级
}
```

### 2. 修改等级要求
修改每个等级所需的搜刮次数：

```java
public String getLevelName() {
    if (totalScavenges >= 2000) return "搜刮之神";      // 提高要求到2000
    if (totalScavenges >= 1000) return "搜刮宗师";      // 提高要求到1000
    if (totalScavenges >= 600) return "搜刮专家";       // 提高要求到600
    // ... 继续修改其他等级要求
}
```

### 3. 增加更多等级
可以添加更多等级：

```java
public String getLevelName() {
    if (totalScavenges >= 5000) return "终极搜刮神";    // 新增等级
    if (totalScavenges >= 2000) return "超级搜刮王";    // 新增等级
    if (totalScavenges >= 1000) return "搜刮之神";
    // ... 其他等级
}

public int getLevel() {
    if (totalScavenges >= 5000) return 12;             // 新增等级12
    if (totalScavenges >= 2000) return 11;             // 新增等级11
    if (totalScavenges >= 1000) return 10;
    // ... 其他等级
}
```

## 🎨 奖励显示优化

### 修复前的显示问题
原来显示原始指令：
```
奖励:
- give {player} diamond 3
- give {player} gold_ingot 5
- eco give {player} 500
```

### 修复后的友好显示
现在显示用户友好的格式：
```
奖励:
- 钻石 x3
- 金锭 x5
- 500 金币
```

## 🔧 奖励显示配置

### 支持的奖励类型
系统会自动识别并格式化以下奖励类型：

#### 1. 物品奖励 (give指令)
```yaml
rewards:
  - "give {player} diamond 5"        # 显示为: 钻石 x5
  - "give {player} emerald 3"        # 显示为: 绿宝石 x3
  - "give {player} gold_ingot 10"    # 显示为: 金锭 x10
```

#### 2. 金币奖励 (eco指令)
```yaml
rewards:
  - "eco give {player} 500"          # 显示为: 500 金币
  - "eco give {player} 1000"         # 显示为: 1000 金币
```

#### 3. 特殊奖励
```yaml
rewards:
  - "broadcast {player} 获得了荣誉"   # 显示为: 全服广播荣誉
  - "effect {player} 1 60 2"         # 显示为: 特殊效果
  - "tell {player} 恭喜你！"          # 显示为: 特殊消息
```

### 支持的物品名称映射
系统内置了常用物品的中文名称：

```java
case "DIAMOND": return "钻石";
case "EMERALD": return "绿宝石";
case "GOLD_INGOT": return "金锭";
case "IRON_INGOT": return "铁锭";
case "BREAD": return "面包";
case "EXPERIENCE_BOTTLE": return "经验瓶";
case "DIAMOND_SWORD": return "钻石剑";
case "GOLDEN_APPLE": return "金苹果";
case "TRIPWIRE_HOOK": return "幸运钥匙";
case "FEATHER": return "飞行羽毛";
case "GOLD_NUGGET": return "财富符咒";
case "POTION": return "治疗药水";
// ... 更多物品
```

## 📝 自定义物品名称

### 添加新的物品名称映射
在 `QuestGUI.java` 的 `getFriendlyMaterialName` 方法中添加：

```java
case "YOUR_CUSTOM_ITEM": return "你的自定义物品名称";
case "SPECIAL_SWORD": return "特殊剑";
case "MAGIC_WAND": return "魔法棒";
```

### 修改现有物品名称
直接修改对应的case：

```java
case "DIAMOND": return "💎钻石";           // 添加表情符号
case "EMERALD": return "🟢绿宝石";         // 添加表情符号
case "GOLD_INGOT": return "🟡金锭";        // 添加表情符号
```

## 🎯 排行榜类型配置

### 当前排行榜类型
系统支持5种排行榜类型：

1. **TOTAL_SCAVENGES** - 总搜刮次数
2. **CHESTS_COMPLETED** - 完成搜刮箱
3. **RARE_ITEMS_FOUND** - 稀有物品发现
4. **QUESTS_COMPLETED** - 任务完成
5. **OVERALL_SCORE** - 综合评分

### 综合评分算法
在 `PlayerStats.java` 中的 `calculateScore` 方法：

```java
public double calculateScore() {
    return totalScavenges * 2.0 + 
           chestsCompleted * 3.0 + 
           rareItemsFound * 5.0 + 
           questsCompleted * 10.0;
}
```

### 修改评分权重
可以调整不同活动的权重：

```java
public double calculateScore() {
    return totalScavenges * 1.0 +      // 降低搜刮次数权重
           chestsCompleted * 5.0 +     // 提高完成箱子权重
           rareItemsFound * 10.0 +     // 提高稀有物品权重
           questsCompleted * 15.0;     // 提高任务完成权重
}
```

## 🔄 应用配置更改

### 重启服务器
修改等级配置后需要重启服务器才能生效。

### 重载插件
使用命令重载插件：
```
/scavenge reload
```

### 清除缓存
如果需要重新计算所有玩家的等级，可能需要删除 `player_stats.yml` 文件让系统重新生成。

## 📊 等级进度显示

### 下一等级要求
系统会自动计算并显示下一等级所需的搜刮次数：

```java
public int getNextLevelRequirement() {
    int currentLevel = getLevel();
    // 返回下一等级所需的总搜刮次数
    switch (currentLevel) {
        case 1: return 5;
        case 2: return 10;
        case 3: return 25;
        // ... 其他等级
        default: return totalScavenges; // 已达到最高等级
    }
}
```

## 🎮 玩家体验优化

### 等级提升通知
可以在玩家升级时发送通知（需要在相关代码中添加）：

```java
// 检查是否升级
int oldLevel = getLevel();
// 更新数据后
int newLevel = getLevel();
if (newLevel > oldLevel) {
    player.sendMessage("§6恭喜！你升级到了 " + getLevelName() + "！");
}
```

这样配置后，等级排名系统就可以完全按照你的需求进行自定义了！

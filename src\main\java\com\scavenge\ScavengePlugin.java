package com.scavenge;

import com.scavenge.quest.QuestManager;
import com.scavenge.leaderboard.LeaderboardManager;
import com.scavenge.level.LevelManager;
import com.scavenge.listeners.ItemCollectionListener;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.Bukkit;

public class ScavengePlugin extends JavaPlugin {

    private static ScavengePlugin instance;
    private FileConfiguration config;
    private ScavengeManager scavengeManager;
    private ScavengeChestManager scavengeChestManager;
    private HologramManager hologramManager;
    private QuestManager questManager;
    private LeaderboardManager leaderboardManager;
    private LevelManager levelManager;
    private WorldRestrictionManager worldRestrictionManager;

    @Override
    public void onEnable() {
        instance = this;

        // 保存默认配置文件
        saveDefaultConfig();
        config = getConfig();

        // 初始化管理器
        scavengeManager = new ScavengeManager(this);
        scavengeChestManager = new ScavengeChestManager(this);
        hologramManager = new HologramManager(this);
        questManager = new QuestManager(this);
        leaderboardManager = new LeaderboardManager(this);
        levelManager = new LevelManager(this);
        worldRestrictionManager = new WorldRestrictionManager(this);

        // 注册命令和Tab补全
        getCommand("scavenge").setExecutor(new ScavengeCommand(this));
        this.getCommand("scavenge").setTabCompleter(new ScavengeTabCompleter(this));

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(new ScavengeListener(this), this);
        Bukkit.getPluginManager().registerEvents(new ItemCollectionListener(this), this);
        Bukkit.getPluginManager().registerEvents(worldRestrictionManager, this);

        // 启动全局重置检查任务
        startGlobalResetTask();

        getLogger().info("搜刮插件已启用!");
    }

    @Override
    public void onDisable() {
        // 清理全息图
        if (hologramManager != null) {
            hologramManager.removeAllHolograms();
        }

        // 关闭搜刮箱管理器
        if (scavengeChestManager != null) {
            scavengeChestManager.shutdown();
        }

        // 保存任务数据
        if (questManager != null) {
            questManager.shutdown();
        }

        // 保存排行榜数据
        if (leaderboardManager != null) {
            leaderboardManager.shutdown();
        }

        getLogger().info("搜刮插件已禁用!");
    }

    public static ScavengePlugin getInstance() {
        return instance;
    }

    public ScavengeManager getScavengeManager() {
        return scavengeManager;
    }

    public ScavengeChestManager getScavengeChestManager() {
        return scavengeChestManager;
    }

    public HologramManager getHologramManager() {
        return hologramManager;
    }

    public QuestManager getQuestManager() {
        return questManager;
    }

    public LeaderboardManager getLeaderboardManager() {
        return leaderboardManager;
    }

    public LevelManager getLevelManager() {
        return levelManager;
    }

    public WorldRestrictionManager getWorldRestrictionManager() {
        return worldRestrictionManager;
    }

    /**
     * 重新加载插件配置
     */
    public void reloadPluginConfig() {
        try {
            // 重新加载配置文件
            reloadConfig();
            config = super.getConfig();

            // 重新加载搜刮管理器的配置（奖励配置等）
            if (scavengeManager != null) {
                scavengeManager.reloadConfig();
            }

            // 重新加载搜刮箱管理器的配置（重置时间等）
            if (scavengeChestManager != null) {
                scavengeChestManager.reloadConfig();
            }

            getLogger().info("配置文件已成功重新加载!");
        } catch (Exception e) {
            getLogger().severe("重新加载配置时出错: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public FileConfiguration getConfig() {
        if (config == null) {
            config = super.getConfig();
        }
        return config;
    }

    public String getMessage(String key) {
        return getConfig().getString("messages." + key, "&c消息未找到: " + key)
                .replace("&", "§");
    }

    public String getMessage(String key, String placeholder, String value) {
        return getMessage(key).replace("{" + placeholder + "}", value);
    }

    /**
     * 启动全局重置检查任务
     */
    private void startGlobalResetTask() {
        // 每30秒检查一次所有搜刮箱的重置时间
        Bukkit.getScheduler().runTaskTimer(this, () -> {
            if (scavengeChestManager != null) {
                scavengeChestManager.checkAllChestsForReset();
            }
        }, 600L, 600L); // 30秒 = 600 ticks

        getLogger().info("全局重置检查任务已启动 (每30秒检查一次)");
    }
}

# 强制关闭GUI功能实现

## 🎯 **功能概述**

当搜刮箱重置时（无论是时间到达自动重置还是管理员指令重置），系统会强制关闭所有正在使用该搜刮箱的玩家GUI，确保数据一致性和用户体验。

## 🔧 **实现方案**

### **核心功能**
1. **自动检测** - 识别所有打开特定搜刮箱GUI的玩家
2. **强制关闭** - 立即关闭相关玩家的GUI界面
3. **消息通知** - 向受影响的玩家发送重置通知
4. **日志记录** - 记录强制关闭操作用于调试

### **触发时机**
- ⏰ **定时重置** - 重置时间到达时自动触发
- 🔧 **指令重置** - 管理员使用 `/scavenge reset` 命令时
- 🔄 **手动重置** - 任何调用 `reset()` 方法的情况

## 📝 **代码实现**

### **1. 强制关闭GUI方法**
在 `ScavengeChest.java` 中新增：

```java
/**
 * 强制关闭所有打开此搜刮箱的GUI
 */
private void forceCloseAllGUIs() {
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    if (plugin == null) return;
    
    // 获取所有在线玩家
    for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
        // 检查玩家是否打开了此搜刮箱的GUI
        ScavengeChestGUI activeGUI = ScavengeChestGUI.getActiveGUI(onlinePlayer);
        if (activeGUI != null && activeGUI.getScavengeChest().equals(this)) {
            // 关闭GUI并发送消息
            onlinePlayer.closeInventory();
            onlinePlayer.sendMessage(plugin.getMessage("force-reset-message"));
            plugin.getLogger().info("强制关闭玩家 " + onlinePlayer.getName() + " 的搜刮箱GUI (重置)");
        }
    }
}
```

### **2. 重置方法集成**
修改 `reset()` 方法：

```java
public void reset() {
    // 强制关闭所有打开此搜刮箱的GUI
    forceCloseAllGUIs();
    
    this.lastResetTime = System.currentTimeMillis();
    this.isActive = false;
    this.activePlayer = null;
    this.hasBeenInitializedWithPlayer = false;
    this.isOverCompleted = false; // 重置超额完成状态
    initializeItems(null);

    // 移除全息图
    ScavengePlugin plugin = ScavengePlugin.getInstance();
    if (plugin != null && plugin.getHologramManager() != null) {
        plugin.getHologramManager().removeHologram(location);
    }
}
```

### **3. GUI关联方法**
在 `ScavengeChestGUI.java` 中新增：

```java
/**
 * 获取关联的搜刮箱
 */
public ScavengeChest getScavengeChest() {
    return scavengeChest;
}
```

## 🎮 **使用场景**

### **场景1：定时自动重置**

#### **情况描述**
- **玩家A**: 正在搜刮箱子，已搜索3个物品
- **玩家B**: 同时打开了同一个搜刮箱的GUI
- **系统**: 重置时间到达（5分钟后）

#### **系统行为**
1. **检测活跃GUI**: 发现玩家A和B都在使用该搜刮箱
2. **强制关闭**: 立即关闭两个玩家的GUI
3. **发送通知**: 向两个玩家发送重置消息
4. **执行重置**: 清空搜刮箱数据，重新初始化
5. **日志记录**: 记录强制关闭操作

#### **玩家体验**
- **GUI突然关闭**: 搜刮界面立即消失
- **收到消息**: `"搜刮箱已重置，GUI已关闭。"`
- **可以重新打开**: 右键搜刮箱显示全新的物品

### **场景2：管理员指令重置**

#### **情况描述**
- **玩家C**: 正在进行搜刮，搜索进度50%
- **管理员**: 执行 `/scavenge reset all` 命令
- **系统**: 立即重置所有搜刮箱

#### **系统行为**
1. **遍历所有搜刮箱**: 检查每个搜刮箱的活跃GUI
2. **批量强制关闭**: 关闭所有相关的GUI
3. **统一通知**: 向所有受影响玩家发送消息
4. **批量重置**: 重置所有搜刮箱状态
5. **操作反馈**: 向管理员显示重置数量

#### **预期日志**
```
[INFO] 强制关闭玩家 PlayerC 的搜刮箱GUI (重置)
[INFO] 搜刮箱已强制重置: world:100:64:200
[INFO] 已重置 15 个搜刮箱!
```

### **场景3：单个搜刮箱重置**

#### **情况描述**
- **玩家D**: 正在搜刮特定箱子
- **管理员**: 使用 `/scavenge reset target` 重置该箱子
- **系统**: 只重置目标搜刮箱

#### **系统行为**
1. **定位目标箱子**: 确定管理员指向的搜刮箱
2. **检查活跃GUI**: 只检查该搜刮箱的GUI
3. **精确关闭**: 只关闭使用该箱子的玩家GUI
4. **单独重置**: 只重置目标搜刮箱
5. **精确通知**: 只向相关玩家发送消息

## 🔄 **技术细节**

### **GUI跟踪机制**
```java
// 静态映射跟踪活跃GUI
private static final Map<UUID, ScavengeChestGUI> activeGUIs = new HashMap<>();

// 获取玩家的活跃GUI
public static ScavengeChestGUI getActiveGUI(Player player) {
    return activeGUIs.get(player.getUniqueId());
}
```

### **搜刮箱比较**
```java
// 通过equals方法比较搜刮箱实例
if (activeGUI != null && activeGUI.getScavengeChest().equals(this)) {
    // 确认是同一个搜刮箱
}
```

### **消息配置**
```yaml
messages:
  force-reset-message: "&c搜刮箱已重置，GUI已关闭。"
```

## 🛡️ **安全保障**

### **1. 空指针检查**
- 检查插件实例是否存在
- 验证玩家是否在线
- 确认GUI实例有效性

### **2. 状态同步**
- 强制关闭在重置前执行
- 确保GUI状态与搜刮箱状态一致
- 防止数据不一致问题

### **3. 异常处理**
- 捕获GUI关闭过程中的异常
- 记录错误日志便于调试
- 确保重置过程不被中断

## 📊 **性能考虑**

### **1. 效率优化**
- 只遍历在线玩家，不检查离线玩家
- 使用HashMap快速查找活跃GUI
- 避免不必要的对象创建

### **2. 批量操作**
- 支持批量重置多个搜刮箱
- 一次性处理所有相关GUI
- 减少重复的玩家遍历

### **3. 内存管理**
- 及时清理GUI映射
- 避免内存泄漏
- 正确处理玩家断线情况

## 🎯 **用户体验**

### **1. 清晰通知**
- 明确告知玩家GUI被关闭的原因
- 提供重新打开的指引
- 避免玩家困惑

### **2. 数据保护**
- 确保玩家已获得的奖励不丢失
- 保护搜刮进度的一致性
- 防止重复奖励或丢失奖励

### **3. 操作连续性**
- 玩家可以立即重新打开搜刮箱
- 新的搜刮会话正常开始
- 不影响其他功能的使用

## 🔧 **管理员工具**

### **1. 重置命令**
- `/scavenge reset all` - 重置所有搜刮箱
- `/scavenge reset target` - 重置目标搜刮箱
- 自动处理GUI关闭和玩家通知

### **2. 日志监控**
- 详细记录强制关闭操作
- 显示受影响的玩家信息
- 便于问题排查和监控

### **3. 配置选项**
- 可自定义强制重置消息
- 支持多语言消息配置
- 灵活的重置时间设置

现在搜刮箱重置时会自动强制关闭所有相关的GUI，确保数据一致性和良好的用户体验！

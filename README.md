# 我的世界1.8.8搜刮插件 (ScavengePlugin)

一个为Minecraft 1.8.8设计的高级搜刮插件，具有持久化搜刮箱系统和动态搜索动画。

## 🎯 核心功能

### 搜刮箱系统
- 🎁 **管理员给予物品** - 管理员给玩家特殊的搜刮方块物品
- 🏗️ **玩家放置搜刮箱** - 玩家拿物品放置后变成持久化搜刮箱
- 🔄 **持久化状态** - 玩家关闭后重新打开仍显示之前的内容
- ⏰ **重置机制** - 搜刮完成后需要等待配置的时间才能重新搜刮
- 🎲 **动态搜索** - 打开后自动开始搜索流程，显示进度条
- 🔄 **进度条重置** - 搜索中关闭箱子，重新打开时进度条重置

### 搜索机制详解
1. **未搜索状态** - 显示灰色玻璃板（可配置材质和颜色）
2. **搜索中状态** - 显示红色玻璃板和动态进度条 `&c&l探索中 0%` 到 `&c&l探索中 100%`
3. **探索完成** - 显示根据几率获得的物品，然后继续下一个
4. **自动搜索** - 箱子保持打开状态，自动搜索下一个物品
5. **已领取物品消失** - 玩家领取的物品不再显示
6. **随机位置显示** - 搜刮项目随机分布在箱子的不同位置
7. **无背景填充** - 只在有搜刮项目的位置显示内容，其他位置保持空白
8. **随机奖励** - 根据配置的几率给予奖励或空奖励

## 🎮 使用流程

### 管理员操作
1. 使用 `/scavenge give <玩家名>` 给予玩家搜刮方块
2. 玩家拿搜刮方块放置到地面上
3. 玩家右键点击搜刮箱开始搜刮
4. 使用 `/scavenge list` 查看所有搜刮箱状态

### 玩家体验
1. **获得搜刮方块** - 从管理员处获得特殊的搜刮方块
2. **放置搜刮箱** - 将方块放置到地面上变成搜刮箱
3. **右键打开** - 右键点击搜刮箱打开搜刮界面
4. **观看搜索动画** - 自动搜索显示进度条（0-100%）
5. **收集奖励** - 点击已完成的物品收集到背包
6. **等待重置** - 搜刮完成后需要等待重置时间

## 🔧 命令系统

| 命令 | 描述 | 权限 |
|------|------|------|
| `/scavenge give <玩家名>` | 给予玩家搜刮方块 | `scavenge.admin` |
| `/scavenge place` | 在目标位置放置搜刮箱 | `scavenge.admin` |
| `/scavenge remove` | 移除目标位置的搜刮箱 | `scavenge.admin` |
| `/scavenge list` | 列出所有搜刮箱及状态 | `scavenge.admin` |
| `/scavenge reset all` | 重置所有搜刮箱 | `scavenge.admin` |
| `/scavenge reset target` | 重置目标搜刮箱 | `scavenge.admin` |
| `/scavenge reload` | 重新加载配置文件 | `scavenge.admin` |

## 🔒 权限系统

| 权限 | 描述 | 默认 |
|------|------|------|
| `scavenge.admin` | 允许使用管理员命令 | OP |
| `scavenge.use` | 允许使用搜刮箱 | 所有玩家 |

## ⚙️ 主要配置

### 搜刮方块设置
```yaml
scavenge-block:
  material: CHEST              # 搜刮方块材质
  display-name: "&6&l搜刮箱"   # 显示名称
  lore:                        # 物品描述
    - "&7右键放置后可以搜刮"
    - "&7每次搜刮都有不同的奖励"
  reset-time: 30               # 重置时间（分钟）
```

### GUI配置
```yaml
gui:
  title: "&6&l搜刮奖励"
  size: 27
  random-items: 5              # 随机物品数量

  # 未搜索状态
  unsearched:
    material: STAINED_GLASS_PANE
    data: 7                    # 灰色玻璃板
    display-name: "&7&l未搜索"

  # 搜索中状态
  searching:
    material: STAINED_GLASS_PANE
    data: 14                   # 红色玻璃板
    display-name: "&c&l探索中 {progress}%"
```

### 动画配置
```yaml
animation:
  duration: 3                  # 搜索持续时间（秒）
  update-interval: 4           # 更新间隔（tick）
  next-search-delay: 40        # 下一个搜索延迟（tick）
```

### 奖励配置
```yaml
rewards:
  items:
    diamond:
      material: DIAMOND
      amount: 1-3
      display-name: "&b钻石"
      chance: 15.0             # 15% 几率

    empty:
      material: AIR
      amount: 0
      display-name: "&7空"
      chance: 15.0             # 15% 几率什么都不给
```

## 🎨 特色功能

### 持久化系统
- 搜刮箱状态保存到文件
- 服务器重启后状态保持
- 支持多玩家同时使用不同搜刮箱

### 智能冷却系统
- 可配置的重置时间
- 自动检测冷却状态
- 友好的冷却提示消息

### 动态搜索动画
- 实时进度条显示（0-100%）
- 平滑的动画效果
- 可配置的动画参数
- 探索完成后显示获得的物品

### 智能状态管理
- 进度条在关闭箱子时重置
- 已领取物品不再显示
- 只显示未搜刮的内容

## 🛠️ 安装使用

### 编译安装
1. 确保服务器运行Bukkit/Spigot 1.8.8
2. 编译插件：`mvn clean package`
3. 将jar文件放入`plugins`文件夹
4. 重启服务器

### 快速开始
1. 管理员执行 `/scavenge give 玩家名` 给予搜刮方块
2. 玩家将搜刮方块放置到地面上
3. 玩家右键点击搜刮箱开始搜刮
4. 观看搜索动画并收集奖励
5. 等待重置时间后可再次搜刮

---

## 🎯 最新更新功能

### 动态进度条显示
- **倒计时效果** - 从 `&c&l探索中 100%` 逐个数字倒计时到 `&c&l探索中 0%`
- **无lore干扰** - 只显示进度百分比，不显示额外描述
- **可调节速度** - 每2tick更新一次，总共约10秒完成（100-0）
- **倒计时音效** - 每个数字递减时播放CLICK音效
- **完成音效** - 进度条到0%变成物品时播放铁砧放置音效(ANVIL_LAND)

### 随机位置系统
- **随机分布** - 搜刮项目随机分布在箱子的不同位置
- **可配置数量** - 通过 `gui.random-items` 配置显示的搜刮项目数量
- **无背景填充** - 移除了黑色玻璃板背景，只在有搜刮项目的位置显示内容

### 智能GUI布局
- **关闭按钮** - 位于箱子最后一个位置
- **空白区域** - 没有搜刮项目的位置保持空白
- **动态调整** - 根据配置的箱子大小自动调整布局

---

**插件完成**: 这是一个完整的搜刮插件实现，包含了你要求的所有功能！
- ✅ 管理员给予玩家搜刮方块物品
- ✅ 玩家放置后变成持久化搜刮箱
- ✅ 动态搜索动画和进度条（0-100%）
- ✅ 探索完成显示获得物品，然后继续下一个
- ✅ 进度条重置功能
- ✅ 随机位置显示搜刮项目
- ✅ 无背景填充，保持简洁界面
- ✅ 持久化状态保存
- ✅ 可配置的重置时间
- ✅ 完整的命令和权限系统

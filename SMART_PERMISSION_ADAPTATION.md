# 智能权限适配功能说明

## 🎯 **功能概述**

实现了智能权限适配功能，当不同权限的玩家接手同一个搜刮箱时，系统会：
1. **保留已搜索的物品** - 不重置已经搜索过、已完成、已领取的物品
2. **智能调整总数量** - 根据新玩家权限增加或减少物品数量
3. **更新全息显示** - 正确显示新的总数和进度

## 🔧 **实现方案**

### **核心方法：adaptToPlayerPermission()**

在 `ScavengeChest.java` 中新增的智能适配方法：

```java
public void adaptToPlayerPermission(Player player) {
    // 获取新玩家权限对应的物品数量
    int newItemCount = plugin.getScavengeManager().getRandomItemCountForPlayer(player);
    
    // 计算当前已使用的槽位总数
    int currentTotalSlots = unSearchedSlots.size() + searchingSlots.size() + 
                           completedSlots.size() + claimedSlots.size();
    
    if (newItemCount > currentTotalSlots) {
        // 权限更高，增加物品
        addAdditionalItems(newItemCount - currentTotalSlots);
    } else if (newItemCount < currentTotalSlots) {
        // 权限更低，减少未搜索的物品
        removeUnSearchedItems(currentTotalSlots - newItemCount);
    }
    
    // 标记为已根据玩家权限初始化
    this.hasBeenInitializedWithPlayer = true;
}
```

### **GUI逻辑更新**

在 `ScavengeChestGUI.java` 的 `setupGUI()` 方法中：

```java
if (scavengeChest.needsPlayerInitialization() || isChestEmpty()) {
    // 完全重置（首次初始化或空箱子）
    scavengeChest.resetWithPlayer(player);
} else {
    // 智能权限适配（保留已搜索的物品，调整总数量）
    scavengeChest.adaptToPlayerPermission(player);
}
```

## 🎮 **使用场景**

### **场景1：低权限到高权限**

#### **初始状态**
- **玩家A（普通）**: 5个物品权限
- **搜刮进度**: 搜索了2个，领取了1个，剩余2个未搜索
- **状态**: `已搜索:1, 已完成:1, 已领取:1, 未搜索:2`

#### **玩家B（VIP）接手**
- **玩家B（VIP）**: 10个物品权限
- **智能适配**: 保留已有的4个物品，增加6个新物品
- **最终状态**: `已搜索:1, 已完成:1, 已领取:1, 未搜索:7` (总计10个)

#### **预期日志**
```
[INFO] === 智能权限适配 ===
[INFO] 玩家: VIPPlayer
[INFO] 新权限物品数量: 10
[INFO] 当前总槽位数: 4
[INFO] 需要增加物品数量: 6
[INFO] 添加新物品到槽位: 4
[INFO] 添加新物品到槽位: 5
[INFO] 添加新物品到槽位: 6
[INFO] 添加新物品到槽位: 7
[INFO] 添加新物品到槽位: 8
[INFO] 添加新物品到槽位: 9
[INFO] === 适配完成 ===
[INFO] 最终总数: 10
```

### **场景2：高权限到低权限**

#### **初始状态**
- **玩家A（VIP）**: 10个物品权限
- **搜刮进度**: 搜索了3个，领取了2个，剩余5个未搜索
- **状态**: `已搜索:1, 已完成:2, 已领取:2, 未搜索:5`

#### **玩家B（普通）接手**
- **玩家B（普通）**: 5个物品权限
- **智能适配**: 保留已搜索/已完成/已领取的5个，移除3个未搜索的
- **最终状态**: `已搜索:1, 已完成:2, 已领取:2, 未搜索:2` (总计5个)

#### **预期日志**
```
[INFO] === 智能权限适配 ===
[INFO] 玩家: NormalPlayer
[INFO] 新权限物品数量: 5
[INFO] 当前总槽位数: 8
[INFO] 需要移除物品数量: 3
[INFO] 移除未搜索物品槽位: 7
[INFO] 移除未搜索物品槽位: 8
[INFO] 移除未搜索物品槽位: 9
[INFO] === 适配完成 ===
[INFO] 最终总数: 5
```

## 🔄 **适配逻辑**

### **增加物品时**
1. **计算需要增加的数量**: `newItemCount - currentTotalSlots`
2. **找到可用槽位**: 避免与现有物品冲突
3. **添加未搜索物品**: 创建灰色玻璃板显示
4. **预设搜索速度**: 根据随机奖励设置进度条时间

### **减少物品时**
1. **计算需要减少的数量**: `currentTotalSlots - newItemCount`
2. **只移除未搜索物品**: 保护已搜索、已完成、已领取的物品
3. **清理相关数据**: 移除槽位的搜索速度设置
4. **保持现状**: 如果无法减少到目标数量，保持当前状态

### **保护机制**
- ✅ **已搜索的物品**: 不会被移除
- ✅ **已完成的物品**: 不会被移除
- ✅ **已领取的物品**: 不会被移除
- ❌ **未搜索的物品**: 可以被移除（权限降低时）

## 🎯 **全息图更新**

### **进度显示适配**
智能适配后，全息图会正确显示新的进度：

#### **适配前（普通玩家5个物品）**
```
§e§l探索中 §f2/5 §7(已领取: 1)
```

#### **适配后（VIP玩家10个物品）**
```
§e§l探索中 §f2/10 §7(已领取: 1)
```

### **倒计时显示**
当所有物品完成并领取后，倒计时全息图会根据最终的物品数量显示：
```
§c§l重置倒计时: §f4分32秒
§7已完成: 10/10 物品
```

## 🧪 **测试场景**

### **测试1：权限升级**
1. **普通玩家打开搜刮箱** → 显示5个物品
2. **搜索2个物品** → 1个已完成，1个已领取
3. **VIP玩家接手** → 智能适配到10个物品
4. **验证结果**: 保留已搜索的2个，增加5个新物品

### **测试2：权限降级**
1. **VIP玩家打开搜刮箱** → 显示10个物品
2. **搜索5个物品** → 3个已完成，2个已领取
3. **普通玩家接手** → 智能适配到5个物品
4. **验证结果**: 保留已搜索的5个，移除5个未搜索的

### **测试3：权限相同**
1. **VIP玩家A打开搜刮箱** → 显示10个物品
2. **搜索3个物品** → 部分完成
3. **VIP玩家B接手** → 权限相同，无需调整
4. **验证结果**: 保持原有状态不变

### **测试4：多次切换**
1. **普通玩家** → 5个物品，搜索2个
2. **VIP玩家** → 适配到10个物品，搜索3个
3. **普通玩家** → 适配回5个物品，保留已搜索的5个
4. **验证结果**: 每次切换都正确适配

## 📊 **权限配置示例**

### **config.yml配置**
```yaml
gui:
  random-items: 5  # 默认物品数量
  permission-items:
    scavenge.vip:
      random-items: 10
      display-name: "&a&lVIP搜刮箱 &7(10个物品)"
    scavenge.svip:
      random-items: 15
      display-name: "&d&lSVIP搜刮箱 &7(15个物品)"
    scavenge.mvp:
      random-items: 20
      display-name: "&6&lMVP搜刮箱 &7(20个物品)"
    scavenge.admin:
      random-items: 25
      display-name: "&c&l管理员搜刮箱 &7(25个物品)"
```

### **权限设置命令**
```bash
# 给予VIP权限
/lp user PlayerName permission set scavenge.vip true

# 移除VIP权限（降级到普通）
/lp user PlayerName permission unset scavenge.vip

# 升级到SVIP权限
/lp user PlayerName permission set scavenge.svip true
```

## 🚀 **优势特性**

### **1. 无缝切换**
- 不同权限玩家可以无缝接手搜刮箱
- 不会丢失已搜索的进度
- 权限变化立即生效

### **2. 公平性保证**
- 高权限玩家获得更多搜刮机会
- 低权限玩家不会看到超出权限的物品
- 已获得的奖励不会被收回

### **3. 数据一致性**
- 全息图显示与实际状态同步
- 任务进度正确计算
- 统计数据准确记录

### **4. 性能优化**
- 只在必要时进行适配
- 智能检测权限变化
- 最小化数据操作

### **5. 用户体验**
- 权限切换过程透明
- 详细的日志记录便于调试
- 保护玩家已获得的进度

现在搜刮箱支持智能权限适配，不同权限的玩家可以接手同一个搜刮箱，系统会自动调整物品数量并保留已搜索的进度！
